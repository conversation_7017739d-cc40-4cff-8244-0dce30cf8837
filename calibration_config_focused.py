"""
Focused configuration for parameter calibration of the lake ecosystem model.
Only optimizes 13 specific parameters while keeping others constant.

Parameters to optimize: S_N, S_P, u, m, K_B_N, K_B_P, u_max, K_<PERSON>, K_<PERSON>, S_B, L, R_N, R_P
"""

import numpy as np

class FocusedCalibrationConfig:
    """Configuration class for focused parameter calibration (13 parameters only)."""
    
    def __init__(self):
        """Initialize calibration configuration with focused parameter set."""
        
        # Fixed parameters (will not be optimized)
        self.fixed_parameters = {
            'V': 2.5,              # Lake volume (m³)
            'Q': 0.08,             # Outflow rate (m³/day)
            'R_NC': 16.0,          # N:C ratio (mol/mol)
            'R_PC': 110.0,         # P:C ratio (mol/mol)
            'alpha': -0.012,       # Light attenuation (1/m)
            'I': 25.0,             # Light intensity (W/m²)
            'L_N': 0.35,           # N loading (mg/day)
            'L_P': 0.035           # P loading (mg/day)
        }
        
        # Parameters to optimize (13 parameters)
        self.optimization_parameters = [
            'S_N', 'S_P', 'u', 'm', 'K_B_N', 'K_B_P', 'u_max', 
            'K_N', 'K_P', 'S_B', 'L', 'R_N', 'R_P'
        ]
        
        # Parameter bounds [lower_bound, upper_bound] for optimization parameters
        self.parameter_bounds = {
            'S_N': [0.005, 0.05],       # N settling rate (1/day)
            'S_P': [0.005, 0.05],       # P settling rate (1/day)
            'u': [0.01, 0.05],          # Growth rate (1/day)
            'm': [0.005, 0.02],         # Mortality rate (1/day)
            'K_B_N': [0.2, 1.0],        # N half-saturation for recycling (mg/L)
            'K_B_P': [0.2, 1.0],        # P half-saturation for recycling (mg/L)
            'u_max': [1.0, 3.0],        # Maximum growth rate (1/day)
            'K_N': [0.05, 0.3],         # N half-saturation for growth (mg/L)
            'K_P': [0.05, 0.3],         # P half-saturation for growth (mg/L)
            'S_B': [0.002, 0.01],       # Biomass settling (1/day)
            'L': [0.0001, 0.0005],      # Grazing rate (L/mg/day)
            'R_N': [0.1, 0.3],          # N recycling (mg/day)
            'R_P': [0.01, 0.03]         # P recycling (mg/day)
        }
        
        # GA algorithm settings
        self.ga_settings = {
            'population_size': 50,       # Population size
            'max_generations': 100,      # Maximum number of generations
            'crossover_rate': 0.8,       # Crossover probability
            'mutation_rate': 0.1,        # Mutation probability
            'random_seed': 1024,         # Random seed for reproducibility
            'pool_type': 'Process',      # 'Sequential', 'Thread', or 'Process'
            'verbose': True              # Print optimization progress
        }
        
        # Calibration settings
        self.calibration_settings = {
            'objective_function': 'nse',  # 'nse', 'lognse', 'anse', 'kge'
            'weight_N': 1.0,             # Weight for Nitrogen objective
            'weight_P': 1.0,             # Weight for Phosphorus objective  
            'weight_B': 1.0,             # Weight for Biomass objective
            'penalty_failed': 1e4,       # Penalty for failed model runs
            'tolerance': 1e-6,           # Numerical tolerance for model solving
        }
        
        # Output settings
        self.output_settings = {
            'save_results': True,        # Save calibration results
            'output_dir': 'calibration_results',  # Output directory
            'result_filename': 'focused_calibration_results.json',  # Results filename
        }
    
    def get_bounds_array(self):
        """
        Get parameter bounds as arrays for GA optimization (only for optimization parameters).
        
        Returns:
            tuple: (lower_bounds, upper_bounds) as numpy arrays
        """
        lower_bounds = []
        upper_bounds = []
        
        for param_name in self.optimization_parameters:
            bounds = self.parameter_bounds[param_name]
            lower_bounds.append(bounds[0])
            upper_bounds.append(bounds[1])
        
        return np.array(lower_bounds), np.array(upper_bounds)
    
    def get_default_optimization_parameters(self):
        """
        Get default parameter values for optimization parameters (middle of bounds).
        
        Returns:
            numpy.ndarray: Default parameter values for optimization
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        return (lower_bounds + upper_bounds) / 2.0
    
    def create_full_parameter_array(self, optimization_params):
        """
        Create full parameter array by combining optimization parameters with fixed parameters.
        
        Args:
            optimization_params: Array of 13 optimization parameter values
            
        Returns:
            numpy.ndarray: Full 21-parameter array for model
        """
        # Create parameter dictionary
        param_dict = self.fixed_parameters.copy()
        
        # Add optimization parameters
        for i, param_name in enumerate(self.optimization_parameters):
            param_dict[param_name] = optimization_params[i]
        
        # Create full parameter array in the correct order
        full_params = [
            # Basic parameters (8)
            param_dict['V'], param_dict['S_N'], param_dict['S_P'], param_dict['Q'],
            param_dict['u'], param_dict['m'], param_dict['R_NC'], param_dict['R_PC'],
            # Growth parameters (9)
            param_dict['K_B_N'], param_dict['K_B_P'], param_dict['u_max'], 
            param_dict['K_N'], param_dict['K_P'], param_dict['alpha'], 
            param_dict['I'], param_dict['S_B'], param_dict['L'],
            # Input parameters (4)
            param_dict['L_N'], param_dict['L_P'], param_dict['R_N'], param_dict['R_P']
        ]
        
        return np.array(full_params)
    
    def validate_optimization_parameters(self, params):
        """
        Validate optimization parameter values against bounds.
        
        Args:
            params: Optimization parameter array (13 values)
            
        Returns:
            bool: True if all parameters are within bounds
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        params = np.array(params)
        
        return np.all(params >= lower_bounds) and np.all(params <= upper_bounds)
    
    def clip_optimization_parameters(self, params):
        """
        Clip optimization parameter values to bounds.
        
        Args:
            params: Optimization parameter array (13 values)
            
        Returns:
            numpy.ndarray: Clipped parameter values
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        params = np.array(params)
        
        return np.clip(params, lower_bounds, upper_bounds)
    
    def get_optimization_parameter_names(self):
        """
        Get list of optimization parameter names.
        
        Returns:
            list: Optimization parameter names
        """
        return self.optimization_parameters.copy()
    
    def get_all_parameter_names(self):
        """
        Get list of all parameter names in order.
        
        Returns:
            list: All parameter names in model order
        """
        return [
            # Basic parameters (8)
            'V', 'S_N', 'S_P', 'Q', 'u', 'm', 'R_NC', 'R_PC',
            # Growth parameters (9)
            'K_B_N', 'K_B_P', 'u_max', 'K_N', 'K_P', 'alpha', 'I', 'S_B', 'L',
            # Input parameters (4)
            'L_N', 'L_P', 'R_N', 'R_P'
        ]
    
    def update_bounds(self, param_name, new_bounds):
        """
        Update bounds for a specific optimization parameter.
        
        Args:
            param_name: Name of parameter to update
            new_bounds: [lower_bound, upper_bound]
        """
        if param_name in self.parameter_bounds:
            self.parameter_bounds[param_name] = new_bounds
        else:
            raise ValueError(f"Parameter {param_name} is not an optimization parameter")
    
    def update_fixed_parameter(self, param_name, value):
        """
        Update value of a fixed parameter.
        
        Args:
            param_name: Name of fixed parameter
            value: New value
        """
        if param_name in self.fixed_parameters:
            self.fixed_parameters[param_name] = value
        else:
            raise ValueError(f"Parameter {param_name} is not a fixed parameter")
    
    def update_ga_settings(self, **kwargs):
        """
        Update GA algorithm settings.
        
        Args:
            **kwargs: GA settings to update
        """
        for key, value in kwargs.items():
            if key in self.ga_settings:
                self.ga_settings[key] = value
            else:
                raise ValueError(f"Unknown GA setting: {key}")
    
    def update_calibration_settings(self, **kwargs):
        """
        Update calibration settings.
        
        Args:
            **kwargs: Calibration settings to update
        """
        for key, value in kwargs.items():
            if key in self.calibration_settings:
                self.calibration_settings[key] = value
            else:
                raise ValueError(f"Unknown calibration setting: {key}")
    
    def print_configuration(self):
        """Print current configuration summary."""
        print("🔧 Focused Calibration Configuration")
        print("=" * 50)
        print(f"Optimization parameters ({len(self.optimization_parameters)}):")
        for param in self.optimization_parameters:
            bounds = self.parameter_bounds[param]
            print(f"  {param:8s}: [{bounds[0]:8.4f}, {bounds[1]:8.4f}]")
        
        print(f"\nFixed parameters ({len(self.fixed_parameters)}):")
        for param, value in self.fixed_parameters.items():
            print(f"  {param:8s}: {value:8.4f}")
        
        print(f"\nGA settings:")
        for key, value in self.ga_settings.items():
            print(f"  {key}: {value}")

# Default configuration instance
default_focused_config = FocusedCalibrationConfig()
