"""
Configuration file for parameter calibration of the lake ecosystem model.
Defines parameter bounds, GA settings, and calibration options.
"""

import numpy as np

class CalibrationConfig:
    """Configuration class for parameter calibration."""
    
    def __init__(self):
        """Initialize calibration configuration with default settings."""
        
        # Parameter bounds [lower_bound, upper_bound] for each of the 21 parameters
        # These bounds are based on typical lake ecosystem values from literature
        self.parameter_bounds = {
            # Basic parameters (8)
            'V': [0.5, 5.0],            # Lake volume (m³) - more realistic range
            'S_N': [0.005, 0.05],       # N settling rate (1/day) - typical values
            'S_P': [0.005, 0.05],       # P settling rate (1/day) - typical values
            'Q': [0.02, 0.2],           # Outflow rate (m³/day) - moderate range
            'u': [0.01, 0.05],          # Growth rate (1/day) - realistic growth
            'm': [0.005, 0.02],         # Mortality rate (1/day) - typical mortality
            'R_NC': [12.0, 20.0],       # N:C ratio (mol/mol) - literature range
            'R_PC': [80.0, 150.0],      # P:C ratio (mol/mol) - literature range

            # Growth parameters (9)
            'K_B_N': [0.2, 1.0],        # N half-saturation for recycling (mg/L)
            'K_B_P': [0.2, 1.0],        # P half-saturation for recycling (mg/L)
            'u_max': [1.0, 3.0],        # Maximum growth rate (1/day) - realistic max
            'K_N': [0.05, 0.3],         # N half-saturation for growth (mg/L)
            'K_P': [0.05, 0.3],         # P half-saturation for growth (mg/L)
            'alpha': [-0.02, -0.005],   # Light attenuation (1/m) - typical range
            'I': [15.0, 35.0],          # Light intensity (W/m²) - moderate light
            'S_B': [0.002, 0.01],       # Biomass settling (1/day) - realistic settling
            'L': [0.0001, 0.0005],      # Grazing rate (L/mg/day) - moderate grazing

            # Input parameters (4)
            'L_N': [0.2, 0.6],          # N loading (mg/day) - moderate loading
            'L_P': [0.02, 0.06],        # P loading (mg/day) - moderate loading
            'R_N': [0.1, 0.3],          # N recycling (mg/day) - moderate recycling
            'R_P': [0.01, 0.03]         # P recycling (mg/day) - moderate recycling
        }
        
        # GA algorithm settings
        self.ga_settings = {
            'population_size': 100,      # Population size
            'max_generations': 200,      # Maximum number of generations
            'crossover_rate': 0.8,       # Crossover probability
            'mutation_rate': 0.1,        # Mutation probability
            'random_seed': 1024,         # Random seed for reproducibility
            'pool_type': 'Process',      # 'Sequential', 'Thread', or 'Process'
            'verbose': True              # Print optimization progress
        }
        
        # Calibration settings
        self.calibration_settings = {
            'objective_function': 'nse',  # 'nse', 'lognse', 'anse', 'kge'
            'multi_objective': False,     # Use multiple objectives
            'weight_N': 1.0,             # Weight for Nitrogen objective
            'weight_P': 1.0,             # Weight for Phosphorus objective  
            'weight_B': 1.0,             # Weight for Biomass objective
            'penalty_failed': 1e6,       # Penalty for failed model runs
            'tolerance': 1e-6,           # Numerical tolerance for model solving
            'max_attempts': 3            # Max attempts for each parameter set
        }
        
        # Output settings
        self.output_settings = {
            'save_results': True,        # Save calibration results
            'save_plots': True,          # Save diagnostic plots
            'output_dir': 'calibration_results',  # Output directory
            'result_filename': 'calibration_results.json',  # Results filename
            'plot_format': 'png',        # Plot format ('png', 'pdf', 'svg')
            'dpi': 300                   # Plot resolution
        }
    
    def get_bounds_array(self):
        """
        Get parameter bounds as arrays for GA optimization.
        
        Returns:
            tuple: (lower_bounds, upper_bounds) as numpy arrays
        """
        param_names = [
            # Basic parameters (8)
            'V', 'S_N', 'S_P', 'Q', 'u', 'm', 'R_NC', 'R_PC',
            # Growth parameters (9)
            'K_B_N', 'K_B_P', 'u_max', 'K_N', 'K_P', 'alpha', 'I', 'S_B', 'L',
            # Input parameters (4)
            'L_N', 'L_P', 'R_N', 'R_P'
        ]
        
        lower_bounds = []
        upper_bounds = []
        
        for name in param_names:
            bounds = self.parameter_bounds[name]
            lower_bounds.append(bounds[0])
            upper_bounds.append(bounds[1])
        
        return np.array(lower_bounds), np.array(upper_bounds)
    
    def get_default_parameters(self):
        """
        Get default parameter values (middle of bounds).
        
        Returns:
            numpy.ndarray: Default parameter values
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        return (lower_bounds + upper_bounds) / 2.0
    
    def validate_parameters(self, params):
        """
        Validate parameter values against bounds.
        
        Args:
            params: Parameter array or list
            
        Returns:
            bool: True if all parameters are within bounds
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        params = np.array(params)
        
        return np.all(params >= lower_bounds) and np.all(params <= upper_bounds)
    
    def clip_parameters(self, params):
        """
        Clip parameter values to bounds.
        
        Args:
            params: Parameter array or list
            
        Returns:
            numpy.ndarray: Clipped parameter values
        """
        lower_bounds, upper_bounds = self.get_bounds_array()
        params = np.array(params)
        
        return np.clip(params, lower_bounds, upper_bounds)
    
    def get_parameter_names(self):
        """
        Get ordered list of parameter names.
        
        Returns:
            list: Parameter names in order
        """
        return [
            # Basic parameters (8)
            'V', 'S_N', 'S_P', 'Q', 'u', 'm', 'R_NC', 'R_PC',
            # Growth parameters (9)
            'K_B_N', 'K_B_P', 'u_max', 'K_N', 'K_P', 'alpha', 'I', 'S_B', 'L',
            # Input parameters (4)
            'L_N', 'L_P', 'R_N', 'R_P'
        ]
    
    def update_bounds(self, param_name, new_bounds):
        """
        Update bounds for a specific parameter.
        
        Args:
            param_name: Name of parameter to update
            new_bounds: [lower_bound, upper_bound]
        """
        if param_name in self.parameter_bounds:
            self.parameter_bounds[param_name] = new_bounds
        else:
            raise ValueError(f"Unknown parameter: {param_name}")
    
    def update_ga_settings(self, **kwargs):
        """
        Update GA algorithm settings.
        
        Args:
            **kwargs: GA settings to update
        """
        for key, value in kwargs.items():
            if key in self.ga_settings:
                self.ga_settings[key] = value
            else:
                raise ValueError(f"Unknown GA setting: {key}")
    
    def update_calibration_settings(self, **kwargs):
        """
        Update calibration settings.
        
        Args:
            **kwargs: Calibration settings to update
        """
        for key, value in kwargs.items():
            if key in self.calibration_settings:
                self.calibration_settings[key] = value
            else:
                raise ValueError(f"Unknown calibration setting: {key}")

# Default configuration instance
default_config = CalibrationConfig()
