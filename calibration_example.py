"""
Example usage of the parameter calibration module.
Demonstrates how to calibrate lake ecosystem model parameters using GA.
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# Import calibration modules
from parameter_calibration import ParameterCalibrator
from calibration_config import CalibrationConfig
from data_handler import ObservedData, create_synthetic_data, load_data_from_csv
from equation_solver_optimized import run_model_for_calibration

def example_1_synthetic_data():
    """
    Example 1: Calibration with synthetic observed data.
    """
    print("🧪 Example 1: Calibration with Synthetic Data")
    print("=" * 60)
    
    # Create synthetic observed data
    observed_data = create_synthetic_data(
        n_points=15,
        noise_level=0.15,
        random_seed=42
    )
    
    # Print data summary
    observed_data.summary()
    
    # Create calibration configuration
    config = CalibrationConfig()
    
    # Adjust GA settings for faster example
    config.update_ga_settings(
        population_size=50,
        max_generations=100,
        verbose=True
    )
    
    # Set objective function
    config.update_calibration_settings(
        objective_function='nse'
    )
    
    # Create calibrator and run calibration
    calibrator = ParameterCalibrator(observed_data, config)
    results = calibrator.calibrate()
    
    # Save results
    if results and 'best_parameters' in results:
        output_path = calibrator.save_results()
        print(f"\n✅ Example 1 completed successfully!")
        return results
    else:
        print(f"\n❌ Example 1 failed!")
        return None

def example_2_custom_data():
    """
    Example 2: Calibration with custom observed data.
    """
    print("\n🔬 Example 2: Calibration with Custom Data")
    print("=" * 60)
    
    # Create custom observed data (example lake measurements)
    nitrogen_obs = np.array([1.2, 1.5, 1.8, 1.3, 1.6, 1.4, 1.7, 1.1, 1.9, 1.5])
    phosphorus_obs = np.array([0.08, 0.12, 0.15, 0.09, 0.13, 0.11, 0.14, 0.07, 0.16, 0.12])
    biomass_obs = np.array([0.8, 1.2, 1.5, 0.9, 1.3, 1.0, 1.4, 0.7, 1.6, 1.1])
    
    # Create ObservedData object
    observed_data = ObservedData(
        nitrogen=nitrogen_obs,
        phosphorus=phosphorus_obs,
        biomass=biomass_obs,
        metadata={'source': 'custom_example', 'lake_name': 'Example Lake'}
    )
    
    observed_data.summary()
    
    # Create custom configuration
    config = CalibrationConfig()
    
    # Customize parameter bounds (tighter bounds for faster convergence)
    config.update_bounds('u_max', [1.0, 3.0])
    config.update_bounds('alpha', [-0.02, -0.005])
    config.update_bounds('I', [15.0, 35.0])
    
    # Adjust GA settings
    config.update_ga_settings(
        population_size=60,
        max_generations=80,
        crossover_rate=0.9,
        mutation_rate=0.05
    )
    
    # Use KGE objective function
    config.update_calibration_settings(
        objective_function='kge',
        weight_N=1.0,
        weight_P=1.5,  # Give more weight to phosphorus
        weight_B=1.0
    )
    
    # Run calibration
    calibrator = ParameterCalibrator(observed_data, config)
    results = calibrator.calibrate()
    
    if results and 'best_parameters' in results:
        calibrator.save_results(filename='custom_calibration_results.json')
        print(f"\n✅ Example 2 completed successfully!")
        return results
    else:
        print(f"\n❌ Example 2 failed!")
        return None

def example_3_single_variable():
    """
    Example 3: Calibration with only one observed variable.
    """
    print("\n🎯 Example 3: Single Variable Calibration (Nitrogen only)")
    print("=" * 60)
    
    # Create data with only nitrogen observations
    nitrogen_obs = np.array([2.1, 1.8, 2.3, 1.9, 2.0, 2.2, 1.7, 2.4, 1.6, 2.1])
    
    observed_data = ObservedData(
        nitrogen=nitrogen_obs,
        metadata={'source': 'nitrogen_only_example'}
    )
    
    observed_data.summary()
    
    # Configuration for single variable
    config = CalibrationConfig()
    config.update_ga_settings(
        population_size=40,
        max_generations=60
    )
    
    config.update_calibration_settings(
        objective_function='nse'
    )
    
    # Run calibration
    calibrator = ParameterCalibrator(observed_data, config)
    results = calibrator.calibrate()
    
    if results and 'best_parameters' in results:
        calibrator.save_results(filename='nitrogen_only_results.json')
        print(f"\n✅ Example 3 completed successfully!")
        return results
    else:
        print(f"\n❌ Example 3 failed!")
        return None

def compare_results(results_list, labels):
    """
    Compare calibration results from different examples.
    """
    print("\n📊 Comparison of Calibration Results")
    print("=" * 60)
    
    for i, (results, label) in enumerate(zip(results_list, labels)):
        if results and 'best_parameters' in results:
            print(f"\n{label}:")
            print(f"   Best objective: {results['best_objective']:.6f}")
            print(f"   Runtime: {results['runtime_seconds']:.2f} seconds")
            print(f"   Evaluations: {results['n_evaluations']}")
            
            # Show model predictions
            if 'model_predictions' in results:
                pred = results['model_predictions']
                print(f"   Predictions - N: {pred['nitrogen']:.4f}, P: {pred['phosphorus']:.4f}, B: {pred['biomass']:.4f}")
        else:
            print(f"\n{label}: Failed")

def plot_parameter_comparison(results_list, labels):
    """
    Plot comparison of optimized parameters.
    """
    if not any(r and 'best_parameters' in r for r in results_list):
        print("No valid results to plot")
        return
    
    # Get parameter names
    from equation_solver_optimized import get_parameter_info
    param_info = get_parameter_info()
    param_names = param_info['names']
    
    # Create plot
    fig, ax = plt.subplots(figsize=(15, 8))
    
    x = np.arange(len(param_names))
    width = 0.25
    
    for i, (results, label) in enumerate(zip(results_list, labels)):
        if results and 'best_parameters' in results:
            params = results['best_parameters']
            ax.bar(x + i*width, params, width, label=label, alpha=0.8)
    
    ax.set_xlabel('Parameters')
    ax.set_ylabel('Parameter Values')
    ax.set_title('Comparison of Optimized Parameters')
    ax.set_xticks(x + width)
    ax.set_xticklabels(param_names, rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('parameter_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("📈 Parameter comparison plot saved as 'parameter_comparison.png'")

def main():
    """
    Run all calibration examples.
    """
    print("🌊 Lake Ecosystem Parameter Calibration Examples")
    print("=" * 80)
    
    # Run examples
    results1 = example_1_synthetic_data()
    results2 = example_2_custom_data()
    results3 = example_3_single_variable()
    
    # Compare results
    results_list = [results1, results2, results3]
    labels = ['Synthetic Data (NSE)', 'Custom Data (KGE)', 'Nitrogen Only (NSE)']
    
    compare_results(results_list, labels)
    
    # Plot comparison (if matplotlib is available)
    try:
        plot_parameter_comparison(results_list, labels)
    except ImportError:
        print("Matplotlib not available - skipping parameter comparison plot")
    
    print("\n🎉 All examples completed!")
    print("\nNext steps:")
    print("1. Check the 'calibration_results' directory for saved results")
    print("2. Modify the configuration in calibration_config.py for your specific needs")
    print("3. Load your own observed data using data_handler.py functions")
    print("4. Experiment with different objective functions and GA settings")

if __name__ == "__main__":
    main()
