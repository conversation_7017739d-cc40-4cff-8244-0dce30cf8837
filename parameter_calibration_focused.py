"""
Focused parameter calibration module for lake ecosystem model.
Optimizes only 13 specific parameters while keeping others constant.

Parameters to optimize: S_N, S_P, u, m, K_B_N, K_B_P, u_max, K_N, K_P, S_B, L, R_N, R_P
"""

import numpy as np
import time
from pathlib import Path

# Import local modules
from equation_solver_optimized import run_model_for_calibration
from opt_utils import nse, lognse, anse, kge, ga_problem
from calibration_config_focused import FocusedCalibrationConfig, default_focused_config
from data_handler import ObservedData, validate_observed_data, save_calibration_results

class FocusedParameterCalibrator:
    """Focused parameter calibration using Genetic Algorithm (13 parameters only)."""
    
    def __init__(self, observed_data: ObservedData, config: FocusedCalibrationConfig = None):
        """
        Initialize focused parameter calibrator.
        
        Args:
            observed_data: ObservedData object with observations
            config: FocusedCalibrationConfig object (uses default if None)
        """
        self.observed_data = observed_data
        self.config = config or default_focused_config
        
        # Validate inputs
        if not validate_observed_data(observed_data):
            raise ValueError("Invalid observed data")
        
        # Get parameter information
        self.optimization_param_names = self.config.get_optimization_parameter_names()
        self.n_optimization_params = len(self.optimization_param_names)
        
        # Get parameter bounds for optimization parameters only
        self.lower_bounds, self.upper_bounds = self.config.get_bounds_array()
        
        # Initialize results storage
        self.results = {}
        self.best_optimization_parameters = None
        self.best_full_parameters = None
        self.best_objective = None
        
        # Set up objective function
        self._setup_objective_function()
        
        print(f"✅ Focused Calibrator initialized:")
        print(f"   - {self.n_optimization_params} parameters to optimize")
        print(f"   - {len(self.config.fixed_parameters)} parameters kept fixed")
        print(f"   - {self.observed_data.get_data_length()} observations")
        print(f"   - Variables: {', '.join(self.observed_data.get_available_variables())}")
        print(f"   - Objective: {self.config.calibration_settings['objective_function']}")
    
    def _setup_objective_function(self):
        """Setup the objective function based on configuration."""
        obj_name = self.config.calibration_settings['objective_function']
        
        # Map objective function names to functions
        obj_functions = {
            'nse': nse,
            'lognse': lognse, 
            'anse': anse,
            'kge': kge
        }
        
        if obj_name not in obj_functions:
            raise ValueError(f"Unknown objective function: {obj_name}")
        
        self.objective_func = obj_functions[obj_name]
    
    def _evaluate_optimization_parameters(self, optimization_params):
        """
        Evaluate a set of optimization parameters.
        
        Args:
            optimization_params: Array of 13 optimization parameter values
            
        Returns:
            float: Objective function value (negative for minimization)
        """
        try:
            # Ensure parameters are within bounds
            optimization_params = self.config.clip_optimization_parameters(optimization_params)
            
            # Create full parameter array
            full_params = self.config.create_full_parameter_array(optimization_params)
            
            # Run model with full parameters
            solution = run_model_for_calibration(full_params, return_residuals=False)
            
            if solution is None:
                # Model failed - return penalty
                return -self.config.calibration_settings['penalty_failed']
            
            N_pred, P_pred, B_pred = solution
            
            # Check for reasonable solution values
            if not (0.001 <= N_pred <= 50 and 0.0001 <= P_pred <= 5 and 0.001 <= B_pred <= 50):
                # Unrealistic values - return penalty
                return -self.config.calibration_settings['penalty_failed'] * 0.5
            
            # Calculate objective for each available variable
            objectives = []
            weights = []
            
            if self.observed_data.nitrogen is not None:
                try:
                    y_pred = np.full_like(self.observed_data.nitrogen, N_pred)
                    obj_n = self.objective_func(self.observed_data.nitrogen, y_pred)
                    obj_n = np.clip(obj_n, -10, 1)  # Clip extreme values
                    if np.isfinite(obj_n):
                        objectives.append(obj_n)
                        weights.append(self.config.calibration_settings['weight_N'])
                except:
                    pass
            
            if self.observed_data.phosphorus is not None:
                try:
                    y_pred = np.full_like(self.observed_data.phosphorus, P_pred)
                    obj_p = self.objective_func(self.observed_data.phosphorus, y_pred)
                    obj_p = np.clip(obj_p, -10, 1)  # Clip extreme values
                    if np.isfinite(obj_p):
                        objectives.append(obj_p)
                        weights.append(self.config.calibration_settings['weight_P'])
                except:
                    pass
            
            if self.observed_data.biomass is not None:
                try:
                    y_pred = np.full_like(self.observed_data.biomass, B_pred)
                    obj_b = self.objective_func(self.observed_data.biomass, y_pred)
                    obj_b = np.clip(obj_b, -10, 1)  # Clip extreme values
                    if np.isfinite(obj_b):
                        objectives.append(obj_b)
                        weights.append(self.config.calibration_settings['weight_B'])
                except:
                    pass
            
            # Calculate weighted average objective
            if objectives:
                weighted_obj = np.average(objectives, weights=weights)
                if np.isfinite(weighted_obj):
                    return -weighted_obj  # Negative for minimization
                else:
                    return -self.config.calibration_settings['penalty_failed']
            else:
                return -self.config.calibration_settings['penalty_failed']
                
        except Exception as e:
            # Silently handle errors during optimization
            return -self.config.calibration_settings['penalty_failed']
    
    def calibrate(self):
        """
        Run focused parameter calibration using Genetic Algorithm.
        
        Returns:
            dict: Calibration results
        """
        print("\n🚀 Starting focused parameter calibration...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Setup GA parameters
        ga_settings = self.config.ga_settings
        
        print(f"GA Settings:")
        print(f"   - Population size: {ga_settings['population_size']}")
        print(f"   - Max generations: {ga_settings['max_generations']}")
        print(f"   - Crossover rate: {ga_settings['crossover_rate']}")
        print(f"   - Mutation rate: {ga_settings['mutation_rate']}")
        print(f"   - Pool type: {ga_settings['pool_type']}")
        
        print(f"\nOptimizing parameters: {', '.join(self.optimization_param_names)}")
        
        # Run GA optimization
        try:
            param_opt, _ = ga_problem(
                obj_func=self._evaluate_optimization_parameters,
                n_dim_params=self.n_optimization_params,
                lb=self.lower_bounds,
                ub=self.upper_bounds,
                n_group=ga_settings['population_size'],
                n_iter=ga_settings['max_generations'],
                pco=ga_settings['crossover_rate'],
                pm=ga_settings['mutation_rate'],
                pool_type=ga_settings['pool_type'],
                random_state=ga_settings['random_seed'],
                verbose=ga_settings['verbose']
            )
            
            # Extract results
            if param_opt.BestIndi.sizes != 0:
                self.best_optimization_parameters = param_opt.BestIndi.Phen[0]
                self.best_objective = -param_opt.BestIndi.ObjV[0][0]  # Convert back to positive
                self.best_full_parameters = self.config.create_full_parameter_array(
                    self.best_optimization_parameters
                )
                
                print(f"\n🎉 Calibration completed successfully!")
                print(f"   - Best objective value: {self.best_objective:.6f}")
                print(f"   - Number of evaluations: {param_opt.evalsNum}")
                print(f"   - Runtime: {param_opt.passTime:.2f} seconds")
                
                # Store detailed results
                self.results = {
                    'best_optimization_parameters': self.best_optimization_parameters,
                    'best_full_parameters': self.best_full_parameters,
                    'best_objective': self.best_objective,
                    'optimization_parameter_names': self.optimization_param_names,
                    'all_parameter_names': self.config.get_all_parameter_names(),
                    'fixed_parameters': self.config.fixed_parameters,
                    'parameter_bounds': [self.lower_bounds, self.upper_bounds],
                    'n_evaluations': param_opt.evalsNum,
                    'runtime_seconds': param_opt.passTime,
                    'ga_settings': ga_settings,
                    'calibration_settings': self.config.calibration_settings,
                    'observed_data_summary': {
                        'n_observations': self.observed_data.get_data_length(),
                        'variables': self.observed_data.get_available_variables()
                    }
                }
                
                # Evaluate final model performance
                self._evaluate_final_performance()
                
            else:
                print("❌ No feasible solution found during calibration")
                self.results = {'status': 'failed', 'message': 'No feasible solution found'}
                
        except Exception as e:
            print(f"❌ Calibration failed with error: {e}")
            self.results = {'status': 'failed', 'message': str(e)}
        
        total_time = time.time() - start_time
        print(f"\n⏱️  Total calibration time: {total_time:.2f} seconds")
        
        return self.results
    
    def _evaluate_final_performance(self):
        """Evaluate performance of calibrated parameters."""
        if self.best_full_parameters is None:
            return
        
        print(f"\n📊 Final Model Performance:")
        print("=" * 40)
        
        # Run model with best parameters
        solution = run_model_for_calibration(self.best_full_parameters, return_residuals=False)
        
        if solution is not None:
            N_pred, P_pred, B_pred = solution
            
            print(f"Model predictions:")
            print(f"   - Nitrogen: {N_pred:.6f} mg/L")
            print(f"   - Phosphorus: {P_pred:.6f} mg/L")
            print(f"   - Biomass: {B_pred:.6f} mg/L")
            
            # Calculate individual objectives
            if self.observed_data.nitrogen is not None:
                obj_n = self.objective_func(self.observed_data.nitrogen,
                                          np.full_like(self.observed_data.nitrogen, N_pred))
                print(f"   - Nitrogen {self.config.calibration_settings['objective_function'].upper()}: {obj_n:.6f}")
                self.results['nitrogen_objective'] = obj_n
            
            if self.observed_data.phosphorus is not None:
                obj_p = self.objective_func(self.observed_data.phosphorus,
                                          np.full_like(self.observed_data.phosphorus, P_pred))
                print(f"   - Phosphorus {self.config.calibration_settings['objective_function'].upper()}: {obj_p:.6f}")
                self.results['phosphorus_objective'] = obj_p
            
            if self.observed_data.biomass is not None:
                obj_b = self.objective_func(self.observed_data.biomass,
                                          np.full_like(self.observed_data.biomass, B_pred))
                print(f"   - Biomass {self.config.calibration_settings['objective_function'].upper()}: {obj_b:.6f}")
                self.results['biomass_objective'] = obj_b
            
            # Store model predictions
            self.results['model_predictions'] = {
                'nitrogen': N_pred,
                'phosphorus': P_pred,
                'biomass': B_pred
            }
        
        # Print optimized parameters
        print(f"\n🔧 Optimized Parameters:")
        print("=" * 50)
        for i, name in enumerate(self.optimization_param_names):
            value = self.best_optimization_parameters[i]
            bounds = [self.lower_bounds[i], self.upper_bounds[i]]
            print(f"   {name:8s}: {value:10.6f} [{bounds[0]:8.4f}, {bounds[1]:8.4f}]")
        
        print(f"\n🔒 Fixed Parameters:")
        print("=" * 30)
        for name, value in self.config.fixed_parameters.items():
            print(f"   {name:8s}: {value:10.6f}")
    
    def save_results(self, output_dir=None, filename=None):
        """
        Save calibration results to file.
        
        Args:
            output_dir: Output directory (uses config default if None)
            filename: Output filename (uses config default if None)
        """
        if not self.results:
            print("No results to save")
            return
        
        output_dir = output_dir or self.config.output_settings['output_dir']
        filename = filename or self.config.output_settings['result_filename']
        
        # Create output directory
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # Save results
        filepath = Path(output_dir) / filename
        save_calibration_results(self.results, filepath)
        
        print(f"💾 Results saved to: {filepath}")
        
        return filepath
