import time
import numpy as np
from scipy.stats import pearsonr

from typing import Optional, Union, Callable, Iterable
import multiprocessing as mp
from multiprocessing import Pool as ProcessPool
from multiprocessing.dummy import Pool as ThreadPool
import geatpy as ea

SEED = 1024
np.random.seed(SEED)

# define the objective functions
def nse(ytrue, ypred):
    ytrue_ = ytrue.copy()
    ypred_ = ypred.copy()
    ypred_[ypred_ <= 0] = ytrue_.min()
    _score = 1 - np.sum((ytrue_ - ypred_) ** 2) / np.sum((ytrue_ - np.mean(ytrue_)) ** 2)
    return _score


def lognse(ytrue, ypred):
    ytrue_ = ytrue.copy()
    ypred_ = ypred.copy()
    ypred_[ypred_ <= 0] = ytrue_.min()
    _score = (1 - np.sum((np.log(ytrue_) - np.log(ypred_)) ** 2) /
              np.sum((np.log(ytrue_) - np.mean(np.log(ytrue_))) ** 2))
    return _score


def anse(ytrue, ypred):
    ytrue_ = ytrue.copy()
    ypred_ = ypred.copy()
    ypred_[ypred_ <= 0] = ytrue_.min()
    _score = (1 - np.sum(np.abs(ytrue_ - ypred_)) /
              np.sum(np.abs(ytrue_ - np.mean(ytrue_))))
    return _score


def kge(ytrue, ypred):
    r = pearsonr(ytrue, ypred)[0]
    beta = np.mean(ypred) / np.mean(ytrue)
    alpha = np.var(ypred) / np.var(ytrue)
    _score = 1 - np.sqrt((1 - r) ** 2 + (beta - 1) ** 2 + (alpha - 1) ** 2)
    return _score


class GAOptimizer(ea.Problem):
    def __init__(self, obj_func, n_dim_params, lb, ub, n_group, pool_type: Optional[str] = 'Sequential'):
        # Save func
        self.obj_func = obj_func
        self.n_dim_params = n_dim_params
        self.n_group = n_group

        # Define the hyperparameters of the optimization problem
        name = 'Parameter tuning'  # initialize problem name
        self.n_dim_target = 1  # initialize the dimension of objective function)
        max_or_mins = [1]  # initialize max or min (flag，1: minimization; -1: maximization)
        var_types = [0] * n_dim_params  # initialize varTypes (type of parameters, 0: continuous; 1: discrete)
        lbin = [1] * n_dim_params  # boundary of parameters (0: not containing; 1: containing)
        ubin = [1] * n_dim_params  # boundary of parameters (0: not containing; 1: containing)

        # Call init method of ea.Problem
        ea.Problem.__init__(self, name, self.n_dim_target, max_or_mins, n_dim_params, var_types,
                            lb, ub, lbin, ubin)

        # Set the pool type: 'Thread' or 'Process'
        self.pool_type = pool_type
        if self.pool_type == 'Thread':
            self.pool = ThreadPool(2)  # set the size of the pool
        elif self.pool_type == 'Process':
            num_cores = int(mp.cpu_count())  # get the cpu count
            self.pool = ProcessPool(num_cores)  # set the size of the pool
        else:
            self.pool = None

    def aimFunc(self, pop):
        # Define objective function
        pop_vars = pop.Phen  # Matrices of decision variables
        if self.pool_type == 'Sequential':
            x = np.zeros((self.n_group, self.n_dim_target))
            i = 0
            for value in pop_vars:
                x[i] = self.obj_func(value)
                i += 1
            pop.ObjV = x
        elif self.pool_type == 'Thread':
            pop.ObjV = np.array(list(self.pool.map(self.obj_func, pop_vars))).reshape(-1, self.n_dim_target)
        elif self.pool_type == 'Process':
            result = self.pool.map_async(self.obj_func, pop_vars)
            result.wait()
            pop.ObjV = np.array(result.get()).reshape(-1, self.n_dim_target)


def ga_problem(obj_func, n_dim_params, lb, ub, n_group, n_iter, pco, pm,
               pool_type=None, random_state=0, verbose=True):
    # set random seed
    np.random.seed(seed=random_state)

    """===============================Instantiation==========================="""
    problem = GAOptimizer(obj_func, n_dim_params, lb, ub, n_group, pool_type=pool_type)
    """=============================Population Setup=========================="""
    encoding = 'BG'  # encoding method
    NIND = n_group  # population size
    field = ea.crtfld(encoding, problem.varTypes, problem.ranges, problem.borders)  # create field describer
    population = ea.Population(encoding, field, NIND)  # instantiate the population
    """=============================Algorithm Setup==========================="""
    param_opt = ea.soea_SEGA_templet(problem, population)  # instantiate a algorithm template
    # max of evolution
    param_opt.MAXGEN = n_iter
    # crossover rate
    param_opt.recOper.XOVR = pco
    # mutation rate
    param_opt.mutOper.Pm = pm
    # number of logging
    param_opt.logTras = 1
    # print logging or not
    param_opt.verbose = verbose
    # visualization style
    # (0: no plot, 1: results plot, 2: objective space animation, 3: decision space animation)
    param_opt.drawing = 0

    """=============================Run Optimization==========================="""
    # Run the template to get the best individual and the population of the last generation
    [best_indi, population] = param_opt.run()

    """==============================Output Results============================"""
    if verbose:
        print('Number of Evaluation: %s' % param_opt.evalsNum)
        print('Runtime: %s sec' % param_opt.passTime)
        if best_indi.sizes != 0:
            print('Optimal value of objective function: %s' % best_indi.ObjV[0][0])
            print('Optimal value of decision variables: ')
            for i in range(best_indi.Phen.shape[1]):
                print(best_indi.Phen[0, i])
        else:
            print('No feasible solution found.')

    # terminate the pool after optimization
    if pool_type in ['Process', 'Thread']:
        time.sleep(2)
        problem.pool.terminate()
        problem.pool.join()

    return param_opt, population
