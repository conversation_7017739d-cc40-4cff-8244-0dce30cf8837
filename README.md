# NPB Lake Ecosystem Model with Parameter Calibration

A comprehensive modeling framework for Nitrogen, Phosphorus, and Biomass dynamics in lake ecosystems, featuring automated parameter calibration using Genetic Algorithm optimization.

## 🌊 Model Description

The NPB model simulates the steady-state dynamics of nitrogen (N), phosphorus (P), and biomass (B) in lake ecosystems through a system of coupled differential equations:

$$
\begin{aligned}
    \frac{dN}{dt} &= \frac{L_N}{V} + \frac{R_N}{V} \cdot \frac{B}{B + K_B^N} - S_N \cdot N - \frac{Q}{V} \cdot N - u \cdot B \cdot R^{NC} + m \cdot B \cdot R^{NC} \\
    \frac{dP}{dt} &= \frac{L_P}{V} + \frac{R_P}{V} \cdot \frac{B}{B + K_B^P} - S_P \cdot P - \frac{Q}{V} \cdot P - u \cdot B \cdot R^{PC} + m \cdot B \cdot R^{PC} \\
    \frac{dB}{dt} &= u_{\text{max}} \cdot \min\left(\frac{N}{N + K_N}, \frac{P}{P + K_P}\right) \cdot (1 - e^{\alpha I}) \cdot B - m \cdot B - S_B \cdot B - L \cdot B^2 - \frac{Q}{V} \cdot B
\end{aligned}
$$

At steady state, these equations equal zero:

$$
\left\{
\begin{aligned}
    & \frac{L_N}{V} + \frac{R_N}{V} \cdot \frac{B}{B + K_B^N} - S_N \cdot N - \frac{Q}{V} \cdot N - u \cdot B \cdot R^{NC} + m \cdot B \cdot R^{NC} = 0 \\
    & \frac{L_P}{V} + \frac{R_P}{V} \cdot \frac{B}{B + K_B^P} - S_P \cdot P - \frac{Q}{V} \cdot P - u \cdot B \cdot R^{PC} + m \cdot B \cdot R^{PC} = 0 \\
    & u_{\text{max}} \cdot \min\left(\frac{N}{N + K_N}, \frac{P}{P + K_P}\right) \cdot (1 - e^{\alpha I}) \cdot B - m \cdot B - S_B \cdot B - L \cdot B^2 - \frac{Q}{V} \cdot B = 0
\end{aligned}
\right.
$$

## 🚀 Features

- **Robust Equation Solver**: Multiple numerical methods for finding steady-state solutions
- **Parameter Calibration**: Genetic Algorithm optimization to fit model parameters to observed data
- **Multiple Objective Functions**: NSE, log-NSE, ANSE, and KGE for model-data comparison
- **Flexible Data Input**: Support for CSV files, dictionaries, and synthetic data generation
- **Comprehensive Configuration**: Customizable parameter bounds and optimization settings
- **Results Analysis**: Detailed performance metrics and parameter sensitivity analysis
- **Parallel Processing**: Multi-core support for faster optimization

## 📁 File Structure

```
NPBModel/
├── equation_solver_optimized.py    # Core model equations and solver
├── parameter_calibration.py        # Main calibration module
├── calibration_config.py          # Configuration settings
├── data_handler.py                # Data input/output utilities
├── opt_utils.py                   # Optimization utilities and objective functions
├── calibration_example.py         # Usage examples
└── README.md                      # This file
```

## 🔧 Installation

### Requirements

```bash
pip install numpy scipy pandas matplotlib geatpy
```

### Dependencies

- **numpy**: Numerical computations
- **scipy**: Optimization and root finding
- **pandas**: Data handling
- **matplotlib**: Plotting (optional)
- **geatpy**: Genetic Algorithm framework

## 📖 Quick Start

### Basic Usage

```python
from parameter_calibration import ParameterCalibrator
from data_handler import create_synthetic_data
from calibration_config import CalibrationConfig

# 1. Create or load observed data
observed_data = create_synthetic_data(n_points=20, noise_level=0.1)

# 2. Configure calibration settings
config = CalibrationConfig()
config.update_ga_settings(population_size=100, max_generations=200)

# 3. Run calibration
calibrator = ParameterCalibrator(observed_data, config)
results = calibrator.calibrate()

# 4. Save results
calibrator.save_results()
```

### Loading Your Own Data

```python
from data_handler import load_data_from_csv, ObservedData

# From CSV file
observed_data = load_data_from_csv(
    'your_data.csv',
    nitrogen_col='N_obs',
    phosphorus_col='P_obs',
    biomass_col='B_obs'
)

# From arrays
observed_data = ObservedData(
    nitrogen=[1.2, 1.5, 1.8, 1.3],
    phosphorus=[0.08, 0.12, 0.15, 0.09],
    biomass=[0.8, 1.2, 1.5, 0.9]
)
```

## 🎯 Model Parameters

The model includes **21 parameters** organized into three categories:

### Basic Parameters (8)
- `V`: Lake volume (m³)
- `S_N`, `S_P`: Nitrogen and phosphorus settling rates (1/day)
- `Q`: Outflow rate (m³/day)
- `u`: Growth rate (1/day)
- `m`: Mortality rate (1/day)
- `R_NC`, `R_PC`: Nitrogen:Carbon and Phosphorus:Carbon ratios (mol/mol)

### Growth Parameters (9)
- `K_B_N`, `K_B_P`: Half-saturation constants for nutrient recycling (mg/L)
- `u_max`: Maximum growth rate (1/day)
- `K_N`, `K_P`: Half-saturation constants for growth (mg/L)
- `alpha`: Light attenuation coefficient (1/m)
- `I`: Light intensity (W/m²)
- `S_B`: Biomass settling rate (1/day)
- `L`: Grazing rate (L/mg/day)

### Input Parameters (4)
- `L_N`, `L_P`: Nitrogen and phosphorus loading rates (mg/day)
- `R_N`, `R_P`: Nitrogen and phosphorus recycling rates (mg/day)

## 🔍 Calibration Process

### 1. Objective Functions

Choose from multiple objective functions to compare model predictions with observations:

- **NSE** (Nash-Sutcliffe Efficiency): Standard hydrological metric
- **log-NSE**: NSE applied to log-transformed values (emphasizes low flows)
- **ANSE** (Absolute NSE): Uses absolute differences instead of squared
- **KGE** (Kling-Gupta Efficiency): Balances correlation, bias, and variability

### 2. Genetic Algorithm Optimization

The calibration uses a robust GA implementation with:

- **Population-based search**: Explores parameter space efficiently
- **Multi-core processing**: Parallel evaluation for faster optimization
- **Adaptive operators**: Crossover and mutation for global optimization
- **Constraint handling**: Ensures parameters stay within physical bounds

### 3. Multi-objective Calibration

Optimize multiple variables simultaneously with customizable weights:

```python
config.update_calibration_settings(
    objective_function='nse',
    weight_N=1.0,      # Nitrogen weight
    weight_P=1.5,      # Phosphorus weight (higher priority)
    weight_B=1.0       # Biomass weight
)
```

## 📊 Examples

Run the provided examples to see the calibration in action:

```bash
python calibration_example.py
```

This will run three examples:
1. **Synthetic Data**: Calibration with computer-generated observations
2. **Custom Data**: Multi-objective calibration with weighted objectives
3. **Single Variable**: Calibration using only nitrogen observations

## ⚙️ Configuration

### Parameter Bounds

Customize parameter bounds in `calibration_config.py`:

```python
config = CalibrationConfig()
config.update_bounds('u_max', [1.0, 3.0])  # Tighter bounds for faster convergence
config.update_bounds('alpha', [-0.02, -0.005])
```

### GA Settings

Adjust genetic algorithm parameters:

```python
config.update_ga_settings(
    population_size=100,    # Larger population for better exploration
    max_generations=200,    # More generations for convergence
    crossover_rate=0.8,     # Crossover probability
    mutation_rate=0.1,      # Mutation probability
    pool_type='Process'     # Use multiprocessing
)
```

### Calibration Options

Fine-tune calibration behavior:

```python
config.update_calibration_settings(
    objective_function='kge',
    penalty_failed=1e6,     # Penalty for failed model runs
    tolerance=1e-6,         # Numerical tolerance
    max_attempts=3          # Retry attempts for each parameter set
)
```

## 📈 Results Analysis

The calibration produces comprehensive results including:

- **Best parameter values** with bounds checking
- **Objective function values** for each variable
- **Model predictions** vs. observations
- **Performance metrics** (runtime, evaluations)
- **Convergence diagnostics**

Results are automatically saved to JSON format for further analysis.

## 🔬 Advanced Usage

### Custom Objective Functions

Define your own objective function:

```python
def custom_objective(y_true, y_pred):
    # Your custom metric here
    return metric_value

# Use in calibration
calibrator.objective_func = custom_objective
```

### Batch Calibration

Calibrate multiple datasets:

```python
datasets = [data1, data2, data3]
results = []

for i, data in enumerate(datasets):
    calibrator = ParameterCalibrator(data, config)
    result = calibrator.calibrate()
    calibrator.save_results(filename=f'results_{i}.json')
    results.append(result)
```

### Parameter Sensitivity Analysis

Analyze parameter sensitivity:

```python
# Run calibration with different parameter bounds
bounds_scenarios = [
    {'u_max': [0.5, 2.0]},
    {'u_max': [1.0, 4.0]},
    {'u_max': [2.0, 6.0]}
]

for scenario in bounds_scenarios:
    config = CalibrationConfig()
    for param, bounds in scenario.items():
        config.update_bounds(param, bounds)

    calibrator = ParameterCalibrator(observed_data, config)
    results = calibrator.calibrate()
```

## 🛠️ Troubleshooting

### Common Issues

1. **Model fails to converge**
   - Increase `max_generations` in GA settings
   - Adjust parameter bounds to more realistic ranges
   - Check observed data for outliers or errors

2. **Poor calibration performance**
   - Try different objective functions (KGE often works well)
   - Increase population size for better exploration
   - Adjust parameter weights for multi-objective calibration

3. **Slow optimization**
   - Use `pool_type='Process'` for multiprocessing
   - Reduce population size or max generations
   - Tighten parameter bounds based on prior knowledge

4. **Memory issues**
   - Use `pool_type='Sequential'` instead of parallel processing
   - Reduce population size
   - Process data in smaller batches

### Performance Tips

- **Start with synthetic data** to test your setup
- **Use realistic parameter bounds** based on literature values
- **Monitor convergence** by checking objective function improvement
- **Save intermediate results** for long-running optimizations

## 📚 References

### Model Development
- Lake ecosystem modeling principles
- Nutrient cycling in aquatic systems
- Steady-state assumption validity

### Optimization Methods
- Genetic Algorithm theory and applications
- Multi-objective optimization
- Parameter estimation in environmental models

### Objective Functions
- Nash, J.E. and Sutcliffe, J.V. (1970). River flow forecasting through conceptual models
- Gupta, H.V. et al. (2009). Decomposition of the mean squared error and NSE performance criteria
- Kling, H. et al. (2012). Runoff conditions in the upper Danube basin under an ensemble of climate change scenarios

## 🤝 Contributing

Contributions are welcome! Please feel free to:

- Report bugs and issues
- Suggest new features
- Improve documentation
- Add new objective functions
- Optimize performance

## 📄 License

This project is provided as-is for research and educational purposes.

## 📞 Support

For questions and support:
1. Check the examples in `calibration_example.py`
2. Review the configuration options in `calibration_config.py`
3. Examine the data handling utilities in `data_handler.py`

---

**Happy modeling! 🌊**
