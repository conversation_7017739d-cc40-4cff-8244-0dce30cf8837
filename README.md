# NPB Lake Ecosystem Model - Focused Parameter Calibration

A streamlined parameter calibration framework for Nitrogen, Phosphorus, and Biomass dynamics in lake ecosystems. This version optimizes only **13 key parameters** while keeping others fixed for improved efficiency and stability.

## 🌊 Model Description

The NPB model simulates the steady-state dynamics of nitrogen (N), phosphorus (P), and biomass (B) in lake ecosystems:

$$
\begin{aligned}
    \frac{dN}{dt} &= \frac{L_N}{V} + \frac{R_N}{V} \cdot \frac{B}{B + K_B^N} - S_N \cdot N - \frac{Q}{V} \cdot N - u \cdot B \cdot R^{NC} + m \cdot B \cdot R^{NC} \\
    \frac{dP}{dt} &= \frac{L_P}{V} + \frac{R_P}{V} \cdot \frac{B}{B + K_B^P} - S_P \cdot P - \frac{Q}{V} \cdot P - u \cdot B \cdot R^{PC} + m \cdot B \cdot R^{PC} \\
    \frac{dB}{dt} &= u_{\text{max}} \cdot \min\left(\frac{N}{N + K_N}, \frac{P}{P + K_P}\right) \cdot (1 - e^{\alpha I}) \cdot B - m \cdot B - S_B \cdot B - L \cdot B^2 - \frac{Q}{V} \cdot B
\end{aligned}
$$

## 🎯 Focused Calibration Approach

### Parameters to Optimize (13)
- **S_N, S_P**: Nitrogen and phosphorus settling rates
- **u, m**: Growth and mortality rates
- **K_B_N, K_B_P**: Half-saturation constants for recycling
- **u_max**: Maximum growth rate
- **K_N, K_P**: Half-saturation constants for growth
- **S_B**: Biomass settling rate
- **L**: Grazing rate
- **R_N, R_P**: Nutrient recycling rates

### Fixed Parameters (8)
- **V**: Lake volume (2.5 m³)
- **Q**: Outflow rate (0.08 m³/day)
- **R_NC, R_PC**: Stoichiometric ratios (16.0, 110.0 mol/mol)
- **α**: Light attenuation (-0.012 1/m)
- **I**: Light intensity (25.0 W/m²)
- **L_N, L_P**: External loading rates (0.35, 0.035 mg/day)

## 🚀 Key Advantages

- ✅ **Faster optimization** (13 vs 21 parameters)
- ✅ **Better convergence** and stability
- ✅ **Improved parameter identifiability**
- ✅ **Physically meaningful constraints**
- ✅ **Reduced computational cost**

## 📁 File Structure

```
NPBModel/
├── equation_solver_optimized.py           # Core model equations and solver
├── parameter_calibration_focused.py       # Focused calibration module (13 params)
├── calibration_config_focused.py         # Focused configuration settings
├── data_handler.py                       # Data input/output utilities
├── opt_utils.py                          # GA optimization and objective functions
├── focused_calibration_example.py        # Usage example
└── README.md                             # This file
```

## 🔧 Installation

```bash
pip install numpy scipy pandas geatpy
```

## 📖 Quick Start

### Basic Usage

```python
from parameter_calibration_focused import FocusedParameterCalibrator
from calibration_config_focused import FocusedCalibrationConfig
from data_handler import ObservedData

# 1. Prepare your observed data
observed_data = ObservedData(
    nitrogen=[1.5, 1.8, 1.6, 1.7],      # mg/L
    phosphorus=[0.12, 0.15, 0.13, 0.14], # mg/L
    biomass=[0.8, 1.0, 0.9, 0.95]        # mg/L
)

# 2. Configure calibration
config = FocusedCalibrationConfig()
config.update_ga_settings(population_size=50, max_generations=100)

# 3. Run calibration
calibrator = FocusedParameterCalibrator(observed_data, config)
results = calibrator.calibrate()

# 4. Save results
calibrator.save_results()
```

### Run Example

```bash
python focused_calibration_example.py
```

## 🔍 Configuration Options

### Modify Fixed Parameters

```python
config = FocusedCalibrationConfig()

# Update fixed parameter values
config.update_fixed_parameter('V', 3.0)      # Change lake volume
config.update_fixed_parameter('I', 30.0)     # Change light intensity

# Update optimization parameter bounds
config.update_bounds('u_max', [1.5, 2.5])    # Tighter bounds for max growth rate
```

### GA Settings

```python
config.update_ga_settings(
    population_size=50,     # Population size
    max_generations=100,    # Number of generations
    crossover_rate=0.8,     # Crossover probability
    mutation_rate=0.1,      # Mutation probability
    pool_type='Process'     # Use multiprocessing
)
```

### Objective Functions

Choose from multiple objective functions:
- **NSE** (Nash-Sutcliffe Efficiency): Standard metric
- **KGE** (Kling-Gupta Efficiency): Balances correlation, bias, and variability
- **ANSE** (Absolute NSE): Uses absolute differences
- **log-NSE**: Emphasizes low values

```python
config.update_calibration_settings(
    objective_function='kge',
    weight_N=1.0,           # Nitrogen weight
    weight_P=1.5,           # Phosphorus weight (higher priority)
    weight_B=1.0            # Biomass weight
)
```

## 📊 Loading Your Own Data

### From CSV File

```python
from data_handler import load_data_from_csv

observed_data = load_data_from_csv(
    'lake_data.csv',
    nitrogen_col='N_measured',
    phosphorus_col='P_measured',
    biomass_col='Biomass_measured'
)
```

### From Arrays

```python
from data_handler import ObservedData

# Your measurement data
nitrogen_measurements = [1.2, 1.5, 1.8, 1.3, 1.6]
phosphorus_measurements = [0.08, 0.12, 0.15, 0.09, 0.13]
biomass_measurements = [0.8, 1.2, 1.5, 0.9, 1.3]

observed_data = ObservedData(
    nitrogen=nitrogen_measurements,
    phosphorus=phosphorus_measurements,
    biomass=biomass_measurements
)
```

## 📈 Results Analysis

The calibration provides:

- ✅ **Optimized parameter values** for the 13 calibration parameters
- ✅ **Fixed parameter values** used in the model
- ✅ **Model performance metrics** (NSE, KGE, etc.)
- ✅ **Convergence statistics** (runtime, evaluations)
- ✅ **Model predictions** vs. observations

Results are saved to `calibration_results/focused_calibration_results.json`

## 🔬 Advanced Usage

### Custom Parameter Sets

```python
# Define your own fixed parameters
config = FocusedCalibrationConfig()

# Modify for your specific lake
config.update_fixed_parameter('V', 5.0)      # Larger lake
config.update_fixed_parameter('Q', 0.15)     # Higher outflow
config.update_fixed_parameter('L_N', 0.5)    # Higher N loading

# Adjust optimization bounds based on your system
config.update_bounds('u_max', [2.0, 4.0])    # Higher growth rates
config.update_bounds('S_N', [0.01, 0.08])    # Different settling range
```

### Batch Processing

```python
# Process multiple lakes
lake_datasets = ['lake1.csv', 'lake2.csv', 'lake3.csv']

for i, dataset in enumerate(lake_datasets):
    observed_data = load_data_from_csv(dataset, ...)
    calibrator = FocusedParameterCalibrator(observed_data, config)
    results = calibrator.calibrate()
    calibrator.save_results(filename=f'lake_{i+1}_results.json')
```

## 🛠️ Troubleshooting

### Common Issues

1. **Model fails to converge**
   - Increase `max_generations` in GA settings
   - Check that fixed parameters are appropriate for your system
   - Verify observed data quality and range

2. **Poor parameter recovery**
   - Try different objective functions (KGE often works well)
   - Increase population size for better exploration
   - Adjust parameter bounds based on literature values

3. **Slow optimization**
   - Use `pool_type='Process'` for multiprocessing
   - Reduce population size or max generations
   - The focused approach is already much faster than full calibration

### Performance Tips

- ✅ **Start with the example** to verify setup
- ✅ **Use realistic parameter bounds** based on your system
- ✅ **Check fixed parameters** - modify if needed for your lake
- ✅ **Monitor convergence** through GA output

## 📚 Key References

- Nash, J.E. and Sutcliffe, J.V. (1970). River flow forecasting through conceptual models
- Gupta, H.V. et al. (2009). Decomposition of the mean squared error and NSE performance criteria
- Lake ecosystem modeling and nutrient cycling principles

## 🎉 Summary

This focused calibration approach provides:

- **Efficient optimization** of 13 key parameters
- **Stable convergence** with physically meaningful constraints
- **Easy configuration** and customization
- **Comprehensive results** analysis
- **Ready-to-use examples** and documentation

Perfect for lake ecosystem parameter estimation with observed N, P, and biomass data!

---

**Happy modeling! 🌊**
