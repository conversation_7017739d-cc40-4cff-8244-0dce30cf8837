"""
Data handling module for parameter calibration.
Handles observed data input/output and validation.
"""

import numpy as np
import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple

class ObservedData:
    """Class to handle observed data for calibration."""
    
    def __init__(self, nitrogen=None, phosphorus=None, biomass=None, 
                 time_points=None, metadata=None):
        """
        Initialize observed data.
        
        Args:
            nitrogen: Observed nitrogen concentrations (mg/L)
            phosphorus: Observed phosphorus concentrations (mg/L)  
            biomass: Observed biomass concentrations (mg/L)
            time_points: Time points for observations (optional)
            metadata: Additional metadata dictionary
        """
        self.nitrogen = np.array(nitrogen) if nitrogen is not None else None
        self.phosphorus = np.array(phosphorus) if phosphorus is not None else None
        self.biomass = np.array(biomass) if biomass is not None else None
        self.time_points = np.array(time_points) if time_points is not None else None
        self.metadata = metadata or {}
        
        self._validate_data()
    
    def _validate_data(self):
        """Validate the observed data."""
        data_arrays = [self.nitrogen, self.phosphorus, self.biomass]
        available_data = [arr for arr in data_arrays if arr is not None]
        
        if not available_data:
            raise ValueError("At least one type of observed data must be provided")
        
        # Check that all provided data have the same length
        lengths = [len(arr) for arr in available_data]
        if len(set(lengths)) > 1:
            raise ValueError("All observed data arrays must have the same length")
        
        # Check time points if provided
        if self.time_points is not None:
            if len(self.time_points) != lengths[0]:
                raise ValueError("Time points must have the same length as observed data")
    
    def get_available_variables(self):
        """
        Get list of available variables.
        
        Returns:
            list: Available variable names
        """
        available = []
        if self.nitrogen is not None:
            available.append('nitrogen')
        if self.phosphorus is not None:
            available.append('phosphorus')
        if self.biomass is not None:
            available.append('biomass')
        return available
    
    def get_data_length(self):
        """
        Get the length of observed data.
        
        Returns:
            int: Number of observations
        """
        for data in [self.nitrogen, self.phosphorus, self.biomass]:
            if data is not None:
                return len(data)
        return 0
    
    def get_data_dict(self):
        """
        Get data as dictionary.
        
        Returns:
            dict: Data dictionary with available variables
        """
        data_dict = {}
        if self.nitrogen is not None:
            data_dict['nitrogen'] = self.nitrogen
        if self.phosphorus is not None:
            data_dict['phosphorus'] = self.phosphorus
        if self.biomass is not None:
            data_dict['biomass'] = self.biomass
        if self.time_points is not None:
            data_dict['time_points'] = self.time_points
        return data_dict
    
    def summary(self):
        """
        Print summary of observed data.
        """
        print("Observed Data Summary:")
        print("=" * 40)
        print(f"Number of observations: {self.get_data_length()}")
        print(f"Available variables: {', '.join(self.get_available_variables())}")
        
        if self.nitrogen is not None:
            print(f"Nitrogen - Mean: {np.mean(self.nitrogen):.4f}, Std: {np.std(self.nitrogen):.4f}, Range: [{np.min(self.nitrogen):.4f}, {np.max(self.nitrogen):.4f}]")
        if self.phosphorus is not None:
            print(f"Phosphorus - Mean: {np.mean(self.phosphorus):.4f}, Std: {np.std(self.phosphorus):.4f}, Range: [{np.min(self.phosphorus):.4f}, {np.max(self.phosphorus):.4f}]")
        if self.biomass is not None:
            print(f"Biomass - Mean: {np.mean(self.biomass):.4f}, Std: {np.std(self.biomass):.4f}, Range: [{np.min(self.biomass):.4f}, {np.max(self.biomass):.4f}]")
        
        if self.time_points is not None:
            print(f"Time range: [{np.min(self.time_points):.2f}, {np.max(self.time_points):.2f}]")
        
        if self.metadata:
            print(f"Metadata: {self.metadata}")

def load_data_from_csv(filepath, nitrogen_col=None, phosphorus_col=None, 
                      biomass_col=None, time_col=None, **kwargs):
    """
    Load observed data from CSV file.
    
    Args:
        filepath: Path to CSV file
        nitrogen_col: Column name for nitrogen data
        phosphorus_col: Column name for phosphorus data
        biomass_col: Column name for biomass data
        time_col: Column name for time data
        **kwargs: Additional arguments for pd.read_csv
        
    Returns:
        ObservedData: Loaded observed data object
    """
    df = pd.read_csv(filepath, **kwargs)
    
    nitrogen = df[nitrogen_col].values if nitrogen_col and nitrogen_col in df.columns else None
    phosphorus = df[phosphorus_col].values if phosphorus_col and phosphorus_col in df.columns else None
    biomass = df[biomass_col].values if biomass_col and biomass_col in df.columns else None
    time_points = df[time_col].values if time_col and time_col in df.columns else None
    
    metadata = {
        'source_file': str(filepath),
        'columns': df.columns.tolist(),
        'shape': df.shape
    }
    
    return ObservedData(nitrogen=nitrogen, phosphorus=phosphorus, 
                       biomass=biomass, time_points=time_points, metadata=metadata)

def load_data_from_dict(data_dict):
    """
    Load observed data from dictionary.
    
    Args:
        data_dict: Dictionary with data arrays
        
    Returns:
        ObservedData: Loaded observed data object
    """
    return ObservedData(
        nitrogen=data_dict.get('nitrogen'),
        phosphorus=data_dict.get('phosphorus'),
        biomass=data_dict.get('biomass'),
        time_points=data_dict.get('time_points'),
        metadata=data_dict.get('metadata', {})
    )

def create_synthetic_data(n_points=20, noise_level=0.1, random_seed=42):
    """
    Create synthetic observed data for testing.
    
    Args:
        n_points: Number of data points
        noise_level: Noise level (fraction of signal)
        random_seed: Random seed for reproducibility
        
    Returns:
        ObservedData: Synthetic observed data
    """
    np.random.seed(random_seed)
    
    # Create synthetic "true" values based on typical lake concentrations
    nitrogen_true = np.random.uniform(0.5, 3.0, n_points)
    phosphorus_true = np.random.uniform(0.05, 0.3, n_points)
    biomass_true = np.random.uniform(0.2, 2.0, n_points)
    
    # Add noise
    nitrogen_obs = nitrogen_true * (1 + np.random.normal(0, noise_level, n_points))
    phosphorus_obs = phosphorus_true * (1 + np.random.normal(0, noise_level, n_points))
    biomass_obs = biomass_true * (1 + np.random.normal(0, noise_level, n_points))
    
    # Ensure positive values
    nitrogen_obs = np.maximum(nitrogen_obs, 0.01)
    phosphorus_obs = np.maximum(phosphorus_obs, 0.001)
    biomass_obs = np.maximum(biomass_obs, 0.01)
    
    time_points = np.linspace(0, 365, n_points)  # One year of data
    
    metadata = {
        'type': 'synthetic',
        'noise_level': noise_level,
        'random_seed': random_seed,
        'n_points': n_points
    }
    
    return ObservedData(nitrogen=nitrogen_obs, phosphorus=phosphorus_obs,
                       biomass=biomass_obs, time_points=time_points, metadata=metadata)

def save_calibration_results(results, filepath):
    """
    Save calibration results to JSON file.

    Args:
        results: Calibration results dictionary
        filepath: Output file path
    """
    def convert_to_serializable(obj):
        """Recursively convert numpy objects to Python native types."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.floating, np.complexfloating)):
            return float(obj)
        elif isinstance(obj, (np.integer, np.signedinteger, np.unsignedinteger)):
            return int(obj)
        elif isinstance(obj, dict):
            return {key: convert_to_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [convert_to_serializable(item) for item in obj]
        else:
            return obj

    # Convert all numpy objects to serializable types
    results_serializable = convert_to_serializable(results)

    with open(filepath, 'w') as f:
        json.dump(results_serializable, f, indent=2)

def load_calibration_results(filepath):
    """
    Load calibration results from JSON file.
    
    Args:
        filepath: Input file path
        
    Returns:
        dict: Calibration results
    """
    with open(filepath, 'r') as f:
        results = json.load(f)
    
    # Convert lists back to numpy arrays where appropriate
    array_keys = ['best_parameters', 'parameter_bounds', 'objective_values']
    for key in array_keys:
        if key in results:
            results[key] = np.array(results[key])
    
    return results

def validate_observed_data(observed_data):
    """
    Validate observed data for calibration.
    
    Args:
        observed_data: ObservedData object
        
    Returns:
        bool: True if data is valid for calibration
    """
    if not isinstance(observed_data, ObservedData):
        print("Error: Data must be an ObservedData object")
        return False
    
    if observed_data.get_data_length() == 0:
        print("Error: No observed data provided")
        return False
    
    available_vars = observed_data.get_available_variables()
    if len(available_vars) == 0:
        print("Error: No valid variables in observed data")
        return False
    
    # Check for negative values
    for var in available_vars:
        data = getattr(observed_data, var)
        if np.any(data <= 0):
            print(f"Warning: {var} contains non-positive values")
    
    print(f"Validation passed: {len(available_vars)} variables, {observed_data.get_data_length()} observations")
    return True
