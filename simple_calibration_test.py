"""
Simple test of the parameter calibration module with improved settings.
"""

import numpy as np
from parameter_calibration import ParameterCalibrator
from calibration_config import CalibrationConfig
from data_handler import create_synthetic_data
from equation_solver_optimized import run_model_for_calibration

def test_simple_calibration():
    """Test calibration with a simple setup."""
    print("🧪 Simple Parameter Calibration Test")
    print("=" * 50)
    
    # Create synthetic observed data with realistic values
    np.random.seed(42)
    
    # Create more realistic synthetic data based on typical lake values
    nitrogen_obs = np.array([1.5, 1.8, 1.6, 1.7, 1.4, 1.9, 1.5, 1.6])
    phosphorus_obs = np.array([0.12, 0.15, 0.13, 0.14, 0.11, 0.16, 0.12, 0.13])
    biomass_obs = np.array([0.8, 1.0, 0.9, 0.95, 0.75, 1.1, 0.85, 0.9])
    
    from data_handler import ObservedData
    observed_data = ObservedData(
        nitrogen=nitrogen_obs,
        phosphorus=phosphorus_obs,
        biomass=biomass_obs,
        metadata={'source': 'simple_test'}
    )
    
    print(f"Observed data: {len(nitrogen_obs)} points")
    print(f"N range: [{np.min(nitrogen_obs):.2f}, {np.max(nitrogen_obs):.2f}]")
    print(f"P range: [{np.min(phosphorus_obs):.3f}, {np.max(phosphorus_obs):.3f}]")
    print(f"B range: [{np.min(biomass_obs):.2f}, {np.max(biomass_obs):.2f}]")
    
    # Create configuration with smaller search space
    config = CalibrationConfig()
    
    # Use smaller GA settings for faster testing
    config.update_ga_settings(
        population_size=30,
        max_generations=50,
        crossover_rate=0.8,
        mutation_rate=0.15,
        pool_type='Sequential',  # Use sequential for debugging
        verbose=True
    )
    
    # Use NSE objective
    config.update_calibration_settings(
        objective_function='nse',
        penalty_failed=1e4  # Smaller penalty
    )
    
    print(f"\nGA Settings:")
    print(f"  Population: {config.ga_settings['population_size']}")
    print(f"  Generations: {config.ga_settings['max_generations']}")
    print(f"  Pool type: {config.ga_settings['pool_type']}")
    
    # Test a few parameter sets manually first
    print(f"\n🔧 Testing parameter evaluation...")
    
    # Get default parameters
    default_params = config.get_default_parameters()
    print(f"Default parameters shape: {default_params.shape}")
    
    # Test model run
    solution = run_model_for_calibration(default_params)
    if solution is not None:
        N, P, B = solution
        print(f"Default model solution: N={N:.3f}, P={P:.3f}, B={B:.3f}")
    else:
        print("Default parameters failed to solve")
    
    # Create calibrator
    try:
        calibrator = ParameterCalibrator(observed_data, config)
        
        # Test objective evaluation
        obj_value = calibrator._evaluate_single_parameter_set(default_params)
        print(f"Default objective value: {obj_value:.6f}")
        
        if obj_value > -1000:  # Not a penalty value
            print("✅ Objective evaluation working correctly")
            
            # Run calibration
            print(f"\n🚀 Starting calibration...")
            results = calibrator.calibrate()
            
            if results and 'best_parameters' in results:
                print(f"\n✅ Calibration successful!")
                print(f"Best objective: {results['best_objective']:.6f}")
                
                # Save results
                calibrator.save_results(filename='simple_test_results.json')
                return True
            else:
                print(f"\n❌ Calibration failed")
                return False
        else:
            print("❌ Objective evaluation returning penalty values")
            return False
            
    except Exception as e:
        print(f"❌ Error during calibration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_bounds():
    """Test that parameter bounds are reasonable."""
    print("\n🔍 Testing parameter bounds...")
    
    config = CalibrationConfig()
    lower_bounds, upper_bounds = config.get_bounds_array()
    
    print(f"Parameter bounds summary:")
    param_names = config.get_parameter_names()
    
    for i, name in enumerate(param_names):
        lb, ub = lower_bounds[i], upper_bounds[i]
        mid = (lb + ub) / 2
        print(f"  {name:8s}: [{lb:8.4f}, {ub:8.4f}] (mid: {mid:8.4f})")
    
    # Test with middle values
    mid_params = (lower_bounds + upper_bounds) / 2
    solution = run_model_for_calibration(mid_params)
    
    if solution is not None:
        N, P, B = solution
        print(f"\nMiddle parameter solution: N={N:.4f}, P={P:.4f}, B={B:.4f}")
        if np.all(np.array([N, P, B]) > 0):
            print("✅ Middle parameters produce positive solution")
            return True
        else:
            print("❌ Middle parameters produce negative values")
            return False
    else:
        print("❌ Middle parameters failed to solve")
        return False

def main():
    """Run simple calibration tests."""
    print("🌊 Simple Parameter Calibration Tests")
    print("=" * 60)
    
    # Test parameter bounds first
    bounds_ok = test_parameter_bounds()
    
    if bounds_ok:
        # Run calibration test
        calibration_ok = test_simple_calibration()
        
        if calibration_ok:
            print(f"\n🎉 All tests passed!")
            print(f"The calibration module is working correctly.")
        else:
            print(f"\n⚠️ Calibration test failed, but bounds are OK.")
            print(f"Try adjusting GA settings or parameter bounds.")
    else:
        print(f"\n❌ Parameter bounds test failed.")
        print(f"Need to adjust parameter bounds in calibration_config.py")

if __name__ == "__main__":
    main()
