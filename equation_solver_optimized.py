from scipy.optimize import root, minimize
import numpy as np

def create_lake_model(params):
    """
    Create a lake ecosystem model with given parameters.
    Returns the equations function and parameter dictionary.
    """
    # Unpack parameters
    V, S_N, S_P, Q, u, m, R_NC, R_PC = params['basic']
    K_B_N, K_B_P, u_max, K_N, K_P, alpha, I, S_B, L = params['growth']
    L_N, L_P, R_N, R_P = params['inputs']
    
    def equations(vars):
        """Lake ecosystem steady-state equations."""
        N, P, B = np.maximum(vars, [1e-10, 1e-10, 1e-10])  # Ensure positive
        
        # Light and nutrient limitation
        exp_term = 1 - np.exp(alpha * I)
        growth_lim = min(N / (N + K_N), P / (P + K_P))
        
        # Nitrogen balance
        eq1 = (L_N / V) + (R_N / V) * (B / (B + K_B_N)) - S_N * N - (Q / V) * N - u * B * R_NC + m * B * R_NC
        
        # Phosphorus balance  
        eq2 = (L_P / V) + (R_P / V) * (B / (B + K_B_P)) - S_P * P - (Q / V) * P - u * B * R_PC + m * B * R_PC
        
        # Biomass balance
        eq3 = u_max * growth_lim * exp_term * B - m * B - S_B * B - L * B**2 - (Q / V) * B
        
        return np.array([eq1, eq2, eq3])
    
    return equations, params

def solve_ecosystem(equations, initial_guesses=None, tolerance=1e-8):
    """
    Solve the ecosystem equations with multiple methods and initial guesses.
    """
    if initial_guesses is None:
        initial_guesses = [
            [1.0, 0.1, 0.5], [2.0, 0.2, 1.0], [3.0, 0.3, 1.5],
            [0.5, 0.05, 0.2], [4.0, 0.4, 2.0]
        ]
    
    best_solution = None
    best_residual = float('inf')
    
    for guess in initial_guesses:
        # Try multiple methods
        methods = ['hybr', 'lm']
        
        for method in methods:
            try:
                sol = root(equations, guess, method=method)
                if sol.success:
                    residuals = equations(sol.x)
                    residual_norm = np.linalg.norm(residuals)
                    
                    # Prioritize positive solutions
                    if np.all(sol.x > 0) and residual_norm < tolerance:
                        return sol.x, residual_norm, "SUCCESS: Positive solution"
                    elif residual_norm < best_residual:
                        best_solution = sol.x
                        best_residual = residual_norm
            except:
                continue
        
        # Try constrained optimization for positive solutions
        try:
            def objective(x):
                return np.sum(equations(x)**2)
            
            bounds = [(1e-6, None), (1e-6, None), (1e-6, None)]
            result = minimize(objective, guess, bounds=bounds, method='L-BFGS-B')
            
            if result.success:
                residual_norm = np.sqrt(result.fun)
                if residual_norm < tolerance:
                    return result.x, residual_norm, "SUCCESS: Constrained positive solution"
                elif residual_norm < best_residual:
                    best_solution = result.x
                    best_residual = residual_norm
        except:
            continue
    
    if best_solution is not None:
        status = "PARTIAL: Best numerical solution (may have negative values)"
        return best_solution, best_residual, status
    else:
        return None, float('inf'), "FAILED: No solution found"

def find_optimal_parameters():
    """
    Systematically search for parameters that yield positive equilibrium.
    """
    print("🔍 Searching for optimal parameters...")
    print("=" * 60)
    
    # Base parameter set
    base_params = {
        'basic': [1.0, 0.01, 0.01, 0.05, 0.02, 0.01, 16.0, 106.0],  # V, S_N, S_P, Q, u, m, R_NC, R_PC
        'growth': [0.5, 0.5, 1.5, 0.1, 0.1, -0.01, 20.0, 0.005, 0.0002],  # K_B_N, K_B_P, u_max, K_N, K_P, alpha, I, S_B, L
        'inputs': [0.3, 0.025, 0.2, 0.015]  # L_N, L_P, R_N, R_P
    }
    
    # Parameter variations to try
    variations = [
        # Increase growth potential
        {'growth': [0.5, 0.5, 2.0, 0.1, 0.1, -0.005, 25.0, 0.003, 0.0001]},
        # Reduce losses
        {'growth': [0.5, 0.5, 1.8, 0.1, 0.1, -0.008, 22.0, 0.004, 0.0001]},
        # Increase inputs
        {'inputs': [0.4, 0.03, 0.25, 0.02]},
        # Combination: high growth + low losses
        {'growth': [0.5, 0.5, 2.2, 0.1, 0.1, -0.003, 30.0, 0.002, 0.00005]},
        # Moderate but balanced
        {'growth': [0.3, 0.3, 1.6, 0.08, 0.08, -0.01, 18.0, 0.006, 0.0002]},
    ]
    
    best_params = None
    best_solution = None
    best_residual = float('inf')
    
    for i, variation in enumerate(variations):
        print(f"\nTesting parameter set {i+1}:")
        
        # Create modified parameters
        test_params = base_params.copy()
        for key, values in variation.items():
            test_params[key] = values
        
        # Print key parameters
        u_max, alpha, I, S_B, L = test_params['growth'][2], test_params['growth'][5], test_params['growth'][6], test_params['growth'][7], test_params['growth'][8]
        L_N, L_P = test_params['inputs'][0], test_params['inputs'][1]
        print(f"  Growth: u_max={u_max}, alpha={alpha}, I={I}")
        print(f"  Losses: S_B={S_B}, L={L}")
        print(f"  Inputs: L_N={L_N}, L_P={L_P}")
        
        # Create and solve model
        equations, _ = create_lake_model(test_params)
        solution, residual, status = solve_ecosystem(equations)
        
        if solution is not None:
            N, P, B = solution
            print(f"  Solution: N={N:.4f}, P={P:.4f}, B={B:.4f}")
            print(f"  Residual: {residual:.2e}")
            print(f"  Status: {status}")
            
            # Check if this is the best solution
            if "SUCCESS" in status and residual < best_residual:
                best_params = test_params
                best_solution = solution
                best_residual = residual
                print(f"  ✅ NEW BEST SOLUTION!")
            elif np.all(solution > 0) and residual < 1e-6 and residual < best_residual:
                best_params = test_params
                best_solution = solution
                best_residual = residual
                print(f"  ✅ NEW BEST POSITIVE SOLUTION!")
        else:
            print(f"  ❌ No solution found")
    
    return best_params, best_solution, best_residual

def main():
    """Main execution function."""
    print("🌊 Lake Ecosystem Equation Solver")
    print("Optimized for finding physically meaningful solutions")
    print("=" * 60)
    
    # Find optimal parameters
    best_params, best_solution, best_residual = find_optimal_parameters()
    
    if best_solution is not None:
        print("\n" + "🎉" * 20)
        print("OPTIMAL SOLUTION FOUND!")
        print("🎉" * 20)
        
        N, P, B = best_solution
        print(f"\n📊 Final Ecosystem State:")
        print(f"  Nitrogen (N):  {N:.6f} mg/L")
        print(f"  Phosphorus (P): {P:.6f} mg/L")
        print(f"  Biomass (B):    {B:.6f} mg/L")
        print(f"  Residual norm:  {best_residual:.2e}")
        
        # Verify solution
        equations, _ = create_lake_model(best_params)
        final_residuals = equations(best_solution)
        print(f"\n🔬 Equation Verification:")
        print(f"  dN/dt = 0: {final_residuals[0]:.2e}")
        print(f"  dP/dt = 0: {final_residuals[1]:.2e}")
        print(f"  dB/dt = 0: {final_residuals[2]:.2e}")
        
        max_residual = np.max(np.abs(final_residuals))
        if max_residual < 1e-8:
            print(f"  ✅ EXCELLENT: All residuals < 1e-8")
        elif max_residual < 1e-6:
            print(f"  ✅ GOOD: All residuals < 1e-6")
        else:
            print(f"  ⚠️  ACCEPTABLE: Max residual = {max_residual:.2e}")
        
        if np.all(best_solution > 0):
            print(f"  ✅ PHYSICAL: All concentrations positive")
            print(f"\n🏆 SUCCESS: Solution is both numerically accurate and physically meaningful!")
        else:
            print(f"  ❌ WARNING: Some concentrations are negative")
    else:
        print("\n❌ No suitable solution found with current parameter ranges.")
        print("Consider expanding the parameter search space.")

if __name__ == "__main__":
    main()
