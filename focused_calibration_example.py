"""
Example of focused parameter calibration.
Optimizes only 13 specific parameters while keeping others constant.

Parameters to optimize: S_N, S_P, u, m, K_B_N, K_B_P, u_max, K_N, K_P, S_B, L, R_N, R_P
"""

import numpy as np
from parameter_calibration_focused import FocusedParameterCalibrator
from calibration_config_focused import FocusedCalibrationConfig
from data_handler import ObservedData, create_synthetic_data
from equation_solver_optimized import run_model_for_calibration, create_lake_model, solve_ecosystem

def create_focused_synthetic_data():
    """Create synthetic data using known parameter values."""
    print("🔬 Creating synthetic data with known parameters...")
    
    # Define true parameter values (including both fixed and optimization parameters)
    true_params = {
        # Fixed parameters
        'V': 2.5, 'Q': 0.08, 'R_NC': 16.0, 'R_PC': 110.0,
        'alpha': -0.012, 'I': 25.0, 'L_N': 0.35, 'L_P': 0.035,
        
        # Optimization parameters (these will be "unknown" for calibration)
        'S_N': 0.02, 'S_P': 0.02, 'u': 0.025, 'm': 0.01,
        'K_B_N': 0.6, 'K_B_P': 0.6, 'u_max': 1.8,
        'K_N': 0.15, 'K_P': 0.15, 'S_B': 0.006,
        'L': 0.0003, 'R_N': 0.18, 'R_P': 0.018
    }
    
    # Create parameter structure for model
    model_params = {
        'basic': [true_params['V'], true_params['S_N'], true_params['S_P'], true_params['Q'],
                  true_params['u'], true_params['m'], true_params['R_NC'], true_params['R_PC']],
        'growth': [true_params['K_B_N'], true_params['K_B_P'], true_params['u_max'],
                   true_params['K_N'], true_params['K_P'], true_params['alpha'],
                   true_params['I'], true_params['S_B'], true_params['L']],
        'inputs': [true_params['L_N'], true_params['L_P'], true_params['R_N'], true_params['R_P']]
    }
    
    # Solve model with true parameters
    equations, _ = create_lake_model(model_params)
    solution, residual, status = solve_ecosystem(equations)
    
    if solution is not None and np.all(solution > 0):
        N_true, P_true, B_true = solution
        print(f"True solution: N={N_true:.4f}, P={P_true:.4f}, B={B_true:.4f}")
        
        # Create observations with noise
        np.random.seed(42)
        n_obs = 12
        noise_level = 0.08  # 8% noise
        
        N_obs = N_true * (1 + np.random.normal(0, noise_level, n_obs))
        P_obs = P_true * (1 + np.random.normal(0, noise_level, n_obs))
        B_obs = B_true * (1 + np.random.normal(0, noise_level, n_obs))
        
        # Ensure positive values
        N_obs = np.maximum(N_obs, 0.01)
        P_obs = np.maximum(P_obs, 0.001)
        B_obs = np.maximum(B_obs, 0.01)
        
        print(f"Observations created:")
        print(f"  N: {np.mean(N_obs):.4f} ± {np.std(N_obs):.4f} (range: {np.min(N_obs):.4f}-{np.max(N_obs):.4f})")
        print(f"  P: {np.mean(P_obs):.4f} ± {np.std(P_obs):.4f} (range: {np.min(P_obs):.4f}-{np.max(P_obs):.4f})")
        print(f"  B: {np.mean(B_obs):.4f} ± {np.std(B_obs):.4f} (range: {np.min(B_obs):.4f}-{np.max(B_obs):.4f})")
        
        # Create ObservedData object
        observed_data = ObservedData(
            nitrogen=N_obs,
            phosphorus=P_obs,
            biomass=B_obs,
            metadata={'true_params': true_params, 'noise_level': noise_level}
        )
        
        # Extract true optimization parameter values for comparison
        optimization_param_names = ['S_N', 'S_P', 'u', 'm', 'K_B_N', 'K_B_P', 'u_max', 
                                   'K_N', 'K_P', 'S_B', 'L', 'R_N', 'R_P']
        true_optimization_params = [true_params[name] for name in optimization_param_names]
        
        return observed_data, true_optimization_params, optimization_param_names
    else:
        print("❌ Failed to create synthetic data - true parameters don't solve")
        return None, None, None

def run_focused_calibration_example():
    """Run focused calibration example."""
    print("🌊 Focused Parameter Calibration Example")
    print("=" * 60)
    
    # Create synthetic data
    observed_data, true_opt_params, param_names = create_focused_synthetic_data()
    
    if observed_data is None:
        print("❌ Failed to create synthetic data")
        return False
    
    # Create focused configuration
    config = FocusedCalibrationConfig()
    
    # Print configuration
    config.print_configuration()
    
    # Adjust GA settings for example
    config.update_ga_settings(
        population_size=40,
        max_generations=50,
        pool_type='Sequential',  # Use sequential for cleaner output
        verbose=True
    )
    
    # Use NSE objective function
    config.update_calibration_settings(
        objective_function='nse'
    )
    
    print(f"\n🎯 Calibration Target:")
    print(f"  Optimize: {len(param_names)} parameters")
    print(f"  Keep fixed: {len(config.fixed_parameters)} parameters")
    print(f"  Objective: {config.calibration_settings['objective_function'].upper()}")
    
    # Test true parameters
    print(f"\n🧪 Testing true optimization parameters...")
    calibrator = FocusedParameterCalibrator(observed_data, config)
    
    true_obj = calibrator._evaluate_optimization_parameters(true_opt_params)
    print(f"True parameters objective: {true_obj:.6f}")
    
    if true_obj > -100:  # Should be a good objective value
        print("✅ True parameters give good objective value")
        
        # Run calibration
        print(f"\n🚀 Starting focused calibration...")
        results = calibrator.calibrate()
        
        if results and 'best_optimization_parameters' in results:
            print(f"\n📊 Calibration Results Summary:")
            print(f"  Best objective: {results['best_objective']:.6f}")
            print(f"  True objective: {-true_obj:.6f}")
            print(f"  Runtime: {results['runtime_seconds']:.2f} seconds")
            print(f"  Evaluations: {results['n_evaluations']}")
            
            # Compare optimization parameters
            best_opt_params = results['best_optimization_parameters']
            
            print(f"\n🔍 Parameter Recovery Analysis:")
            print(f"{'Parameter':<10} {'True':<10} {'Calibrated':<12} {'Error %':<10}")
            print("-" * 50)
            
            total_error = 0
            for i, name in enumerate(param_names):
                true_val = true_opt_params[i]
                cal_val = best_opt_params[i]
                error_pct = abs(cal_val - true_val) / abs(true_val) * 100
                total_error += error_pct
                print(f"{name:<10} {true_val:<10.4f} {cal_val:<12.4f} {error_pct:<10.1f}")
            
            avg_error = total_error / len(param_names)
            print(f"\nAverage parameter error: {avg_error:.1f}%")
            
            # Save results
            calibrator.save_results()
            
            if avg_error < 30:  # Good recovery
                print(f"\n🎉 Excellent parameter recovery!")
                return True
            elif avg_error < 50:  # Reasonable recovery
                print(f"\n✅ Good parameter recovery!")
                return True
            else:
                print(f"\n⚠️ Parameter recovery could be improved.")
                return True
        else:
            print(f"\n❌ Calibration failed to find solution")
            return False
    else:
        print(f"❌ True parameters give poor objective value: {true_obj}")
        print("This suggests an issue with the model setup")
        return False

def test_focused_configuration():
    """Test the focused configuration setup."""
    print("\n🔧 Testing Focused Configuration")
    print("=" * 40)
    
    config = FocusedCalibrationConfig()
    
    # Test parameter array creation
    default_opt_params = config.get_default_optimization_parameters()
    full_params = config.create_full_parameter_array(default_opt_params)
    
    print(f"✅ Optimization parameters: {len(default_opt_params)}")
    print(f"✅ Full parameter array: {len(full_params)}")
    
    # Test model run with default parameters
    solution = run_model_for_calibration(full_params)
    if solution is not None:
        N, P, B = solution
        print(f"✅ Default parameters solve: N={N:.4f}, P={P:.4f}, B={B:.4f}")
        return True
    else:
        print("❌ Default parameters fail to solve")
        return False

def main():
    """Run the focused calibration example."""
    print("🎯 Focused Parameter Calibration - 13 Parameters Only")
    print("=" * 70)
    
    # Test configuration first
    if test_focused_configuration():
        # Run calibration example
        success = run_focused_calibration_example()
        
        if success:
            print(f"\n🎉 Focused calibration example completed successfully!")
            print(f"\nKey advantages of focused calibration:")
            print(f"  ✅ Faster optimization (13 vs 21 parameters)")
            print(f"  ✅ More stable convergence")
            print(f"  ✅ Better parameter identifiability")
            print(f"  ✅ Physically meaningful fixed parameters")
            print(f"\nNext steps:")
            print(f"  1. Check 'focused_calibration_results.json' for detailed results")
            print(f"  2. Modify fixed parameters in calibration_config_focused.py if needed")
            print(f"  3. Use with your own observed data")
        else:
            print(f"\n⚠️ Example had issues - check the output above")
    else:
        print(f"\n❌ Configuration test failed")

if __name__ == "__main__":
    main()
