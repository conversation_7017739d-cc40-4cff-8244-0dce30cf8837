"""
Demonstration of parameter calibration with a working example.
This creates synthetic data from known parameters, then tries to recover them.
"""

import numpy as np
from parameter_calibration import ParameterCalibrator
from calibration_config import CalibrationConfig
from data_handler import ObservedData
from equation_solver_optimized import run_model_for_calibration, create_lake_model, solve_ecosystem

def create_realistic_synthetic_data():
    """Create synthetic data from a known parameter set."""
    print("🔬 Creating realistic synthetic data...")
    
    # Use a parameter set that we know works
    true_params = {
        'basic': [2.5, 0.02, 0.02, 0.08, 0.025, 0.01, 16.0, 110.0],
        'growth': [0.6, 0.6, 1.8, 0.15, 0.15, -0.012, 25.0, 0.006, 0.0003],
        'inputs': [0.35, 0.035, 0.18, 0.018]
    }
    
    # Solve the model with these parameters
    equations, _ = create_lake_model(true_params)
    solution, residual, status = solve_ecosystem(equations)
    
    if solution is not None and np.all(solution > 0):
        N_true, P_true, B_true = solution
        print(f"True solution: N={N_true:.4f}, P={P_true:.4f}, B={B_true:.4f}")
        
        # Add some noise to create "observations"
        np.random.seed(42)
        n_obs = 10
        noise_level = 0.1
        
        # Create observations with noise
        N_obs = N_true * (1 + np.random.normal(0, noise_level, n_obs))
        P_obs = P_true * (1 + np.random.normal(0, noise_level, n_obs))
        B_obs = B_true * (1 + np.random.normal(0, noise_level, n_obs))
        
        # Ensure positive values
        N_obs = np.maximum(N_obs, 0.01)
        P_obs = np.maximum(P_obs, 0.001)
        B_obs = np.maximum(B_obs, 0.01)
        
        print(f"Observations created:")
        print(f"  N: {np.mean(N_obs):.4f} ± {np.std(N_obs):.4f}")
        print(f"  P: {np.mean(P_obs):.4f} ± {np.std(P_obs):.4f}")
        print(f"  B: {np.mean(B_obs):.4f} ± {np.std(B_obs):.4f}")
        
        # Create ObservedData object
        observed_data = ObservedData(
            nitrogen=N_obs,
            phosphorus=P_obs,
            biomass=B_obs,
            metadata={'true_params': true_params, 'noise_level': noise_level}
        )
        
        # Convert true parameters to flat array for comparison
        true_params_flat = np.concatenate([
            true_params['basic'],
            true_params['growth'],
            true_params['inputs']
        ])
        
        return observed_data, true_params_flat
    else:
        print("❌ Failed to create synthetic data - true parameters don't solve")
        return None, None

def run_calibration_demo():
    """Run a complete calibration demonstration."""
    print("🌊 Parameter Calibration Demonstration")
    print("=" * 60)
    
    # Create synthetic data
    observed_data, true_params = create_realistic_synthetic_data()
    
    if observed_data is None:
        print("❌ Failed to create synthetic data")
        return False
    
    # Create configuration
    config = CalibrationConfig()
    
    # Adjust GA settings for demonstration
    config.update_ga_settings(
        population_size=40,
        max_generations=30,
        crossover_rate=0.8,
        mutation_rate=0.1,
        pool_type='Sequential',
        verbose=True
    )
    
    # Use NSE objective function
    config.update_calibration_settings(
        objective_function='nse',
        penalty_failed=1e3  # Moderate penalty
    )
    
    print(f"\n🎯 Calibration Settings:")
    print(f"  Objective: {config.calibration_settings['objective_function']}")
    print(f"  Population: {config.ga_settings['population_size']}")
    print(f"  Generations: {config.ga_settings['max_generations']}")
    
    # Test that we can evaluate the true parameters
    print(f"\n🧪 Testing true parameters...")
    calibrator = ParameterCalibrator(observed_data, config)
    
    true_obj = calibrator._evaluate_single_parameter_set(true_params)
    print(f"True parameters objective: {true_obj:.6f}")
    
    if true_obj > -100:  # Should be a good objective value
        print("✅ True parameters give good objective value")
        
        # Run calibration
        print(f"\n🚀 Starting calibration...")
        results = calibrator.calibrate()
        
        if results and 'best_parameters' in results:
            print(f"\n📊 Calibration Results:")
            print(f"  Best objective: {results['best_objective']:.6f}")
            print(f"  True objective: {-true_obj:.6f}")
            
            # Compare parameters
            best_params = results['best_parameters']
            param_names = config.get_parameter_names()
            
            print(f"\n🔍 Parameter Comparison:")
            print(f"{'Parameter':<10} {'True':<10} {'Calibrated':<12} {'Diff %':<10}")
            print("-" * 50)
            
            total_error = 0
            for i, name in enumerate(param_names):
                true_val = true_params[i]
                cal_val = best_params[i]
                diff_pct = abs(cal_val - true_val) / abs(true_val) * 100
                total_error += diff_pct
                print(f"{name:<10} {true_val:<10.4f} {cal_val:<12.4f} {diff_pct:<10.1f}")
            
            avg_error = total_error / len(param_names)
            print(f"\nAverage parameter error: {avg_error:.1f}%")
            
            # Save results
            calibrator.save_results(filename='calibration_demo_results.json')
            
            if avg_error < 50:  # Reasonable recovery
                print(f"\n🎉 Calibration successful! Parameters recovered reasonably well.")
                return True
            else:
                print(f"\n⚠️ Calibration completed but parameter recovery was poor.")
                return True
        else:
            print(f"\n❌ Calibration failed to find solution")
            return False
    else:
        print(f"❌ True parameters give poor objective value: {true_obj}")
        print("This suggests an issue with the objective function or model setup")
        return False

def test_individual_components():
    """Test individual components to debug issues."""
    print(f"\n🔧 Testing Individual Components")
    print("=" * 40)
    
    # Test parameter bounds
    config = CalibrationConfig()
    lower_bounds, upper_bounds = config.get_bounds_array()
    mid_params = (lower_bounds + upper_bounds) / 2
    
    print(f"1. Testing middle parameters...")
    solution = run_model_for_calibration(mid_params)
    if solution is not None:
        N, P, B = solution
        print(f"   Solution: N={N:.4f}, P={P:.4f}, B={B:.4f}")
        print("   ✅ Middle parameters work")
    else:
        print("   ❌ Middle parameters fail")
        return False
    
    # Test objective functions
    print(f"\n2. Testing objective functions...")
    from opt_utils import nse, kge
    
    y_true = np.array([1.0, 2.0, 3.0])
    y_pred = np.array([1.1, 1.9, 3.1])
    
    nse_val = nse(y_true, y_pred)
    kge_val = kge(y_true, y_pred)
    
    print(f"   NSE: {nse_val:.4f}")
    print(f"   KGE: {kge_val:.4f}")
    
    if nse_val > 0.8 and kge_val > 0.8:
        print("   ✅ Objective functions work correctly")
    else:
        print("   ❌ Objective functions give unexpected values")
        return False
    
    print(f"\n✅ All components working correctly")
    return True

def main():
    """Run the calibration demonstration."""
    # Test components first
    if test_individual_components():
        # Run full demonstration
        success = run_calibration_demo()
        
        if success:
            print(f"\n🎉 Demonstration completed successfully!")
            print(f"\nNext steps:")
            print(f"1. Check 'calibration_demo_results.json' for detailed results")
            print(f"2. Try with your own observed data")
            print(f"3. Experiment with different objective functions")
        else:
            print(f"\n⚠️ Demonstration had issues - check the output above")
    else:
        print(f"\n❌ Component tests failed - check the setup")

if __name__ == "__main__":
    main()
