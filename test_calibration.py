"""
Test script for the parameter calibration module.
Runs basic tests to ensure all components work correctly.
"""

import numpy as np
import sys
from pathlib import Path

def test_imports():
    """Test that all modules can be imported successfully."""
    print("🧪 Testing imports...")
    
    try:
        from equation_solver_optimized import create_lake_model, solve_ecosystem, run_model_for_calibration
        print("   ✅ equation_solver_optimized imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import equation_solver_optimized: {e}")
        return False
    
    try:
        from opt_utils import nse, lognse, anse, kge, ga_problem
        print("   ✅ opt_utils imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import opt_utils: {e}")
        return False
    
    try:
        from calibration_config import CalibrationConfig, default_config
        print("   ✅ calibration_config imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import calibration_config: {e}")
        return False
    
    try:
        from data_handler import ObservedData, create_synthetic_data, validate_observed_data
        print("   ✅ data_handler imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import data_handler: {e}")
        return False
    
    try:
        from parameter_calibration import ParameterCalibrator
        print("   ✅ parameter_calibration imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import parameter_calibration: {e}")
        return False
    
    return True

def test_model_solver():
    """Test the basic model solver functionality."""
    print("\n🔧 Testing model solver...")
    
    from equation_solver_optimized import create_lake_model, solve_ecosystem, get_parameter_info
    
    # Test parameter info
    param_info = get_parameter_info()
    if len(param_info['names']) != 21:
        print(f"   ❌ Expected 21 parameters, got {len(param_info['names'])}")
        return False
    print(f"   ✅ Parameter info: {len(param_info['names'])} parameters")
    
    # Test model creation and solving
    base_params = {
        'basic': [1.0, 0.01, 0.01, 0.05, 0.02, 0.01, 16.0, 106.0],
        'growth': [0.5, 0.5, 1.5, 0.1, 0.1, -0.01, 20.0, 0.005, 0.0002],
        'inputs': [0.3, 0.025, 0.2, 0.015]
    }
    
    equations, _ = create_lake_model(base_params)
    solution, residual, status = solve_ecosystem(equations)
    
    if solution is not None:
        print(f"   ✅ Model solved: N={solution[0]:.4f}, P={solution[1]:.4f}, B={solution[2]:.4f}")
        print(f"   ✅ Residual: {residual:.2e}, Status: {status}")
    else:
        print(f"   ❌ Model failed to solve")
        return False
    
    return True

def test_data_handler():
    """Test data handling functionality."""
    print("\n📊 Testing data handler...")
    
    from data_handler import ObservedData, create_synthetic_data, validate_observed_data
    
    # Test synthetic data creation
    try:
        observed_data = create_synthetic_data(n_points=10, noise_level=0.1)
        print(f"   ✅ Synthetic data created: {observed_data.get_data_length()} points")
        print(f"   ✅ Variables: {', '.join(observed_data.get_available_variables())}")
    except Exception as e:
        print(f"   ❌ Failed to create synthetic data: {e}")
        return False
    
    # Test data validation
    if validate_observed_data(observed_data):
        print("   ✅ Data validation passed")
    else:
        print("   ❌ Data validation failed")
        return False
    
    # Test custom data creation
    try:
        custom_data = ObservedData(
            nitrogen=[1.0, 1.5, 2.0],
            phosphorus=[0.1, 0.15, 0.2],
            biomass=[0.5, 0.8, 1.0]
        )
        print(f"   ✅ Custom data created: {custom_data.get_data_length()} points")
    except Exception as e:
        print(f"   ❌ Failed to create custom data: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration functionality."""
    print("\n⚙️ Testing configuration...")
    
    from calibration_config import CalibrationConfig
    
    try:
        config = CalibrationConfig()
        
        # Test bounds
        lower_bounds, upper_bounds = config.get_bounds_array()
        if len(lower_bounds) != 21 or len(upper_bounds) != 21:
            print(f"   ❌ Expected 21 bounds, got {len(lower_bounds)}, {len(upper_bounds)}")
            return False
        print(f"   ✅ Parameter bounds: {len(lower_bounds)} parameters")
        
        # Test parameter names
        param_names = config.get_parameter_names()
        if len(param_names) != 21:
            print(f"   ❌ Expected 21 parameter names, got {len(param_names)}")
            return False
        print(f"   ✅ Parameter names: {len(param_names)} parameters")
        
        # Test default parameters
        default_params = config.get_default_parameters()
        if len(default_params) != 21:
            print(f"   ❌ Expected 21 default parameters, got {len(default_params)}")
            return False
        print(f"   ✅ Default parameters: {len(default_params)} values")
        
        # Test parameter validation
        if config.validate_parameters(default_params):
            print("   ✅ Parameter validation passed")
        else:
            print("   ❌ Parameter validation failed")
            return False
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False
    
    return True

def test_objective_functions():
    """Test objective functions."""
    print("\n🎯 Testing objective functions...")
    
    from opt_utils import nse, lognse, anse, kge
    
    # Create test data
    y_true = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
    y_pred = np.array([1.1, 1.9, 3.2, 3.8, 5.1])  # Close predictions
    
    try:
        nse_val = nse(y_true, y_pred)
        print(f"   ✅ NSE: {nse_val:.4f}")
        
        lognse_val = lognse(y_true, y_pred)
        print(f"   ✅ log-NSE: {lognse_val:.4f}")
        
        anse_val = anse(y_true, y_pred)
        print(f"   ✅ ANSE: {anse_val:.4f}")
        
        kge_val = kge(y_true, y_pred)
        print(f"   ✅ KGE: {kge_val:.4f}")
        
        # Check that values are reasonable
        if 0.5 <= nse_val <= 1.0:
            print("   ✅ NSE value in reasonable range")
        else:
            print(f"   ⚠️ NSE value outside expected range: {nse_val}")
        
    except Exception as e:
        print(f"   ❌ Objective function test failed: {e}")
        return False
    
    return True

def test_calibration_setup():
    """Test calibration setup without running full optimization."""
    print("\n🚀 Testing calibration setup...")
    
    from parameter_calibration import ParameterCalibrator
    from data_handler import create_synthetic_data
    from calibration_config import CalibrationConfig
    
    try:
        # Create test data
        observed_data = create_synthetic_data(n_points=5, noise_level=0.1)
        
        # Create configuration
        config = CalibrationConfig()
        config.update_ga_settings(population_size=10, max_generations=2)  # Small for testing
        
        # Create calibrator
        calibrator = ParameterCalibrator(observed_data, config)
        print("   ✅ Calibrator created successfully")
        
        # Test parameter evaluation (single evaluation)
        test_params = config.get_default_parameters()
        obj_value = calibrator._evaluate_single_parameter_set(test_params)
        print(f"   ✅ Parameter evaluation: {obj_value:.6f}")
        
        if not np.isfinite(obj_value):
            print("   ❌ Objective value is not finite")
            return False
        
    except Exception as e:
        print(f"   ❌ Calibration setup test failed: {e}")
        return False
    
    return True

def run_all_tests():
    """Run all tests and report results."""
    print("🧪 NPB Model Parameter Calibration - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Model Solver Test", test_model_solver),
        ("Data Handler Test", test_data_handler),
        ("Configuration Test", test_configuration),
        ("Objective Functions Test", test_objective_functions),
        ("Calibration Setup Test", test_calibration_setup)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"\n❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"\n❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The calibration module is ready to use.")
        print("\nNext steps:")
        print("1. Run 'python calibration_example.py' to see examples")
        print("2. Create your own calibration script using the provided templates")
        return True
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
